

# **面向无线信道的鲁棒SVD近似的原则性方法：架构创新与训练协同**

## **第1节：近似误差（AE）度量的深度解析及其策略启示**

为了在“AI使能的无线鲁棒SVD算子”竞赛中追求极致的性能，首先必须对核心评价准则——近似误差（Approximation Error, AE）——进行一次严谨的、深入的解构。该度量不仅是评估模型优劣的最终标准，其数学结构本身也为最优解决方案的设计提供了深刻的启示和明确的指引。AE的定义超越了简单的损失函数，它实际上为模型架构和优化策略描绘了一幅清晰的蓝图 1。

### **1.1 AE公式的数学解构**

竞赛组织方将第 i 个信道样本的近似误差定义为一个由三部分组成的复合指标 1：

AEi​=∥Hlabeli,:,:​​∥F​∥Hlabeli,:,:​​−Ui,:,:​Σi,:,:​Vi,:,:∗​∥F​​+∥Ui,:,:∗​Ui,:,:​−I∥F​+∥Vi,:,:∗​Vi,:,:​−I∥F​  
其中，∥⋅∥F​ 表示矩阵的弗罗贝尼乌斯范数（Frobenius norm），Hlabel​ 是理想信道矩阵，U 和 V 分别是模型输出的左、右奇异矩阵，Σ 是由奇异值向量 s 构成的对角矩阵。这个公式可以被精确地分解为三个关键组成部分，每个部分都衡量了模型性能的一个独特维度。

**第一部分：归一化重构误差**

公式的第一项，即 ∥Hlabel​∥F​∥Hlabel​−UΣV∗∥F​​，是标准化的重构误差。它量化了模型输出的SVD分量 (U,Σ,V) 重构出的信道矩阵与真实的理想信道标签之间的差异。弗罗贝尼乌斯范数本质上是矩阵所有元素平方和的平方根，可以看作是向量L2范数在矩阵上的自然推广。通过除以理想信道矩阵的范数进行归一化，该项度量的是相对误差，这使得评估在不同信道条件（例如，不同信道增益）下都保持公平。从根本上说，这一项将问题的核心定义为一个高维的矩阵回归或矩阵合成任务：神经网络需要学习一个从充满噪声和非理想因素的输入信道 H 到其理想SVD分量的复杂映射，以便最小化重构后的矩阵与目标之间的距离。

**第二、三部分：酉矩阵约束惩罚**

公式的后两项，即 ∥U∗U−I∥F​ 和 ∥V∗V−I∥F​，是酉矩阵约束的惩罚项。在复数矩阵的语境中，一个矩阵 U 被称为酉矩阵（Unitary Matrix），如果其共轭转置 U∗ 是其逆矩阵，即 U∗U=UU∗=I，其中 I 是单位矩阵。对于奇异值分解而言，输出的左奇异矩阵 U∈CM×R 和右奇异矩阵 V∈CN×R 必须满足列向量正交的性质，即它们的列向量构成一个标准正交基。这意味着 U∗U 和 V∗V 都必须等于一个 R×R 的单位矩阵 IR​。

这两项惩罚项直接度量了模型输出的 U 和 V 偏离其酉矩阵（或在实数域中称为正交矩阵）属性的程度。任何非零值都表示模型的输出不满足SVD定义的基本结构约束。因此，这两项的存在将问题从一个纯粹的回归任务，转变为一个在特定几何流形上的约束优化问题。

### **1.2 复合损失的策略启示**

AE度量的复合结构对解决方案的设计方法具有决定性的影响。它强烈地暗示了，一种天真地将所有三项都作为损失函数进行优化的方法，其效率和最终效果可能远不及一种更具原则性的几何方法。

**基于损失的正则化方法的低效性**

一种直接的方法是将AE公式直接转化为神经网络的损失函数。在这种范式下，模型将同时优化三个目标：最小化重构误差、最小化 U 的非酉性、最小化 V 的非酉性。然而，这种方法存在固有的缺陷。优化器（如Adam）在处理这种多目标损失函数时，会试图在三者之间寻求一种平衡。梯度下降的每一步都将是三个分量梯度的一个加权和。这可能导致优化过程陷入一个次优的局部最小值，在这个最小值中，模型可能会“牺牲”一些酉矩阵的精确性来换取重构误差的微小降低，反之亦然。例如，优化器可能会发现，允许 U∗U 与单位矩阵有微小偏差，可以换来重构误差项更大幅度的下降，从而导致整体损失函数值的降低，但最终的AE得分却并非最优。这种方法迫使网络在“做什么”（重构信道）和“怎么做”（输出必须是酉矩阵）之间进行权衡，增加了优化景观的复杂性，并可能阻碍模型达到理论上的最佳性能。

**几何方法的必然性：通过架构设计强制约束**

AE度量的结构清晰地指明了一条更优越的路径：不应将酉矩阵约束视为需要通过损失函数来“鼓励”的属性，而应将其视为必须通过模型架构来“强制执行”的硬性约束。如果一个模型的输出 U 和 V 在其设计上就能保证是酉矩阵，那么AE公式的第二项和第三项将恒等于零（或在数值精度范围内接近于零）。

这一转变具有深远的战略意义。它将一个复杂的多目标优化问题，简化为了一个清晰的单目标优化问题：在 U 和 V 始终满足酉矩阵约束的前提下，仅需最小化重构误差 ∥Hlabel​∥F​∥Hlabel​−UΣV∗∥F​​。这种方法的优势是多方面的：

1. **简化的优化景观**：通过架构设计移除约束惩罚项，损失函数的景观变得更加平滑，优化目标更加单一，使得梯度下降过程更稳定、更高效，更容易收敛到更优的解。  
2. **提升模型容量利用率**：网络的所有参数和学习能力都可以完全集中于学习非理想信道到理想信道SVD的复杂映射关系，而无需分心去“学习”如何生成酉矩阵。  
3. **保证解的有效性**：输出的 U 和 V 始终是数学上有效的奇异矩阵，这对于后续在通信系统中的应用（如预编码）至关重要。

综上所述，对AE度量的深度分析揭示了一个核心的战略方向：解决方案的重点应放在设计一个能够直接参数化并输出酉矩阵的神经网络模块上。通过这种方式，问题被重新定义，从一个在欧几里得空间中进行的、带有复杂惩罚项的回归问题，转变为一个在欧几里得空间中学习一个隐式表示，然后通过一个确定性的、可微的映射将其投影到目标几何流形（即酉矩阵空间）上的问题。这种基于几何深度学习的思路是实现AE极致降低的关键所在，它直接将竞赛的评价准则转化为了对模型架构的特定要求。

## **第2节：面向高保真矩阵变换的架构设计**

在确立了通过架构设计来强制执行酉矩阵约束的核心策略后，下一步是构建一个强大的神经网络骨干，该骨干能够有效地学习从非理想信道矩阵到其理想SVD分量的高度非线性映射。考虑到该任务本质上是一个结构化的预测问题——即从一个二维数据结构（输入矩阵）生成另一个二维数据结构（输出矩阵）——采用U-Net范式的编码器-解码器架构是一个逻辑上和实践上都极为合理的起点 2。

### **2.1 U-Net范式：结构化预测的基石**

U-Net及其变体最初为生物医学图像分割而设计，但其核心思想已广泛应用于各种输入和输出均具有空间结构的“图像到图像”转换任务中。其成功的关键在于两大设计元素：

1. **对称的编码器-解码器结构**：编码器通过一系列下采样操作（如池化或跨步卷积）逐步减小特征图的空间分辨率，同时增加通道数，从而捕获从局部到全局的多层次抽象特征。解码器则通过上采样操作（如转置卷积或插值）逐步恢复空间分辨率，重建精细的输出结构。  
2. **跳跃连接（Skip Connections）**：U-Net的标志性设计是将编码器中每个层级的特征图直接“跳跃”连接到解码器中对应层级的输入。这一机制允许解码器在重建过程中直接利用编码器早期阶段提取的、富含高频细节和精确定位信息的低级特征。对于本赛题中的矩阵变换任务，这意味着网络在生成最终的奇异矩阵时，能够参考输入信道矩阵的原始、精细结构，这对于实现高保真度的重构至关重要。

因此，一个U-Net式的架构为我们的任务提供了一个强大的基础框架，能够同时处理矩阵的宏观结构和微观细节。

### **2.2 编码器骨干分析：两大范式的对决**

编码器的选择对整个模型的性能和效率起着决定性作用。近年来，计算机视觉领域涌现出两大主流范式：以ConvNeXt为代表的现代卷积网络（ConvNet）和以Swin Transformer为代表的层级式Transformer。对这两者进行分析，可以为构建最优模型提供依据。

#### **2.2.1 现代卷积网络：ConvNeXt**

ConvNeXt架构是对经典卷积网络（如ResNet）的一次现代化改造，它系统地借鉴了Vision Transformer的设计理念和训练策略，从而在保持纯卷积结构的同时，实现了与Transformer相媲美的性能 5。其关键改进包括：

* **深度可分离卷积（Depthwise Separable Convolutions）**：将标准卷积分解为深度卷积和逐点卷积，大幅降低了计算量（FLOPs）和参数数量，这直接有助于满足竞赛对模型效率的要求 11。  
* **倒置瓶颈结构（Inverted Bottleneck）**：采用“宽-窄-宽”的通道设计，先通过1x1卷积扩大通道数，再进行深度卷积，最后通过1x1卷积恢复通道数，提升了模型的表达能力。  
* **大卷积核**：使用更大的卷积核（如7x7）来增大感受野，模拟Transformer的全局信息捕获能力。

对于本赛题，ConvNeXt的主要优势在于其固有的归纳偏置（如局部性和平移等变性），这非常适合处理具有局部相关性的信道矩阵数据。更重要的是，其卓越的计算效率使其成为构建轻量级且高性能模型的理想选择，特别是在后续的知识蒸馏阶段，需要一个高效的“学生”模型。

#### **2.2.2 层级式Transformer：Swin Transformer**

Swin Transformer通过引入两个核心创新，成功地将Transformer架构应用于通用的计算机视觉任务 17。

* **窗口化自注意力（Windowed Self-Attention）**：将自注意力的计算限制在不重叠的局部窗口内，使得计算复杂度与图像大小呈线性关系，而非二次方关系，解决了Transformer在处理高分辨率图像时的效率瓶颈。  
* **移位窗口机制（Shifted Window Mechanism）**：在连续的Transformer块之间交替使用常规窗口和移位窗口，实现了跨窗口的信息交互，从而构建了层级式的特征表示并捕获了全局上下文信息。

对于本赛题，Swin Transformer的最大优势在于其强大的长距离依赖建模能力。无线信道矩阵中的元素之间可能存在复杂的、非局部的相关性，这些相关性可能无法被固定大小的卷积核有效捕获。Swin Transformer的自注意力机制能够动态地、基于内容地建立任意两个位置之间的联系，从而更全面地理解信道矩阵的整体结构。Swin-UNet等架构已经证明了其在结构化预测任务中的有效性 24。

### **2.3 提出的混合架构：ConvNeXt-Swin-UNet (CS-UNet)**

单纯依赖任何一种范式都可能存在局限性：纯ConvNet可能缺乏对全局上下文的充分理解，而纯Transformer在处理高分辨率特征图时计算成本高昂，且可能因缺乏卷积的归纳偏置而需要更多数据进行训练。因此，一个结合两者优势的混合架构是实现最优性能的理想选择。近期大量的研究也证实了CNN与Transformer混合架构的优越性 29。

我们提出的ConvNeXt-Swin-UNet (CS-UNet)架构旨在实现这种协同效应：

* **编码器**：编码器的主体部分（即处理高、中分辨率特征图的阶段）将采用轻量级的ConvNeXt块 42。这充分利用了ConvNeXt在处理局部特征时的效率和强大能力，以较低的计算成本快速提取出丰富的多尺度局部特征。  
* **瓶颈层**：在U-Net的瓶颈部分，当特征图的空间分辨率最低时，将引入多个Swin Transformer块 50。在这一阶段，计算全局（或大范围）自注意力的成本最低，而收益最大。瓶颈层的Swin Transformer能够对编码器提取的深度抽象特征进行全局上下文建模，整合来自整个信道矩阵的信息，从而弥补了纯卷积网络的短板。  
* **解码器**：解码器将采用标准的U-Net结构，包含上采样层（如转置卷积）和卷积块，并通过跳跃连接接收来自编码器对应ConvNeXt阶段的特征图。这种设计确保了在重建SVD分量时，既能利用到瓶颈层提供的全局上下文信息，又能利用到跳跃连接传递过来的精细局部细节。解码器的最后几层将被设计为三个独立的分支，分别输出奇异值向量 s 的前驱、左奇异矩阵 U 的前驱和右奇异矩阵 V 的前驱。其中，U 和 V 的前驱将通过第3节详述的几何约束模块进行处理。

这种混合架构的设计逻辑是清晰的：在计算资源敏感的高分辨率阶段使用高效的ConvNet，在对全局信息需求最强的低分辨率阶段使用强大的Transformer。这不仅是一种架构上的拼接，更是一种基于计算效率和表征能力权衡的原则性设计，旨在以最优的资源配比，实现对信道矩阵局部和全局特征的全面、深度理解，从而为后续的高精度SVD近似奠定坚实的基础。

下表1对几种备选的编码器骨干进行了对比分析，进一步阐明了选择混合架构的合理性。

The following table:

**表1：编码器骨干的比较分析**

| 骨干架构 | 关键架构特征 | 主要优势 | 计算复杂度 (FLOPs) | 教师模型适用性 | 学生模型适用性 |
| :---- | :---- | :---- | :---- | :---- | :---- |
| **标准ResNet (基线)** | 残差连接，标准卷积 | 成熟稳定，广泛应用 | 中等 | 良好 | 尚可 |
| **ConvNeXt-Tiny** | 深度可分离卷积，倒置瓶颈 | 极高的计算效率，强大的局部特征提取能力 | 低 | 良好 | 极佳 |
| **Swin-Transformer-Tiny** | 窗口化/移位窗口自注意力 | 强大的全局上下文和长距离依赖建模能力 | 中等偏高 | 极佳 | 尚可 |
| **提出的混合CS-UNet** | ConvNeXt编码器主体 \+ Swin Transformer瓶颈 | 结合了ConvNeXt的效率和Swin Transformer的全局建模能力，实现了性能与效率的最佳平衡 | 中等 | 极佳 | 不适用 |

## **第3节：通过设计强制执行酉矩阵约束：一种几何深度学习方法**

如第1节所分析，实现AE极致降低的关键在于将酉矩阵约束从一个需要优化的损失项转变为一个由模型架构本身保证的内在属性。这需要借助几何深度学习领域的工具，将神经网络的输出从无约束的欧几里得空间，精确地映射到酉矩阵所在的特定数学流形上。

### **3.1 斯蒂费尔流形：解的数学空间**

从数学上讲，一个 n×p 的复数矩阵 U 如果其列向量是标准正交的（即 U∗U=Ip​），那么它就属于一个被称为复数斯蒂费尔流形（Complex Stiefel Manifold）St(n,p) 的空间 52。斯蒂费尔流形是一个非欧几里得的弯曲空间，其上的元素（即具有标准正交列的矩阵）构成了我们问题中

U 和 V 的解空间。直接让神经网络在欧几里得空间中学习并输出一个位于该流形上的点是非常困难的，因为标准的梯度下降更新（即在参数空间中沿负梯度方向走一小步）很可能会使点“脱离”流形。传统的黎曼优化方法通过计算切空间上的梯度并使用“收缩”（retraction）操作将点拉回流形来解决这个问题，但这在标准深度学习框架中实现起来非常复杂 53。

### **3.2 凯莱变换：从无约束到酉矩阵的可微桥梁**

幸运的是，存在一种经典且优雅的数学工具——凯莱变换（Cayley Transform），它可以在无约束的矩阵空间和酉矩阵（或正交矩阵）流形之间建立一座可微的桥梁 54。

**标准凯莱变换及其局限性**

对于一个斜埃尔米特矩阵（Skew-Hermitian Matrix）A（即满足 A∗=−A），标准凯莱变换定义为：

W=(I−A)(I+A)−1

变换后的矩阵 W 是一个酉矩阵。这个变换提供了一个从一个线性空间（斜埃尔米特矩阵空间，它是酉群的李代数）到酉矩阵流形的映射。然而，标准凯莱变换有一个显著的局限性：它无法表示任何一个包含-1作为其特征值的酉矩阵。因为如果 W 有一个特征值为-1，那么 I+W 将是奇异的，导致其逆矩阵不存在。  
**缩放凯莱变换（Scaled Cayley Transform）**

为了克服这一局限性，从而能够表示整个酉矩阵空间，引入了缩放凯莱变换 60。其形式为：

W=D(I−A)(I+A)−1

或者等价地：

W=(I−A)(I+A)−1D′

其中 D 和 D′ 是对角矩阵，其对角线元素为复平面上的单位模长复数（通常是+1或-1）。通过引入这个额外的缩放/旋转矩阵 D，缩放凯莱变换能够覆盖整个酉矩阵流形，包括那些含有-1特征值的矩阵。这对于确保我们的模型具有足够的表达能力来近似任何可能的奇异向量至关重要。  
**参数化策略**

这种变换的核心思想是，我们不再让神经网络直接学习目标酉矩阵 U 和 V。相反，我们让网络学习它们的无约束“前驱体”——即两个斜埃尔米特矩阵 AU​ 和 AV​。然后，将这两个前驱体通过一个固定的、不可训练但完全可微的缩放凯莱变换层，即可生成严格满足酉矩阵约束的 U 和 V。

这种方法将学习任务从一个困难的约束优化问题，转变为一个神经网络非常擅长的无约束回归问题。网络只需在欧几里得空间中自由地调整 AU​ 和 AV​ 的参数，而凯莱变换层则负责保证最终的输出始终位于正确的斯蒂费尔流形上。通过链式法则，损失函数关于 U 和 V 的梯度可以被正确地反向传播到 AU​ 和 AV​ 的参数上。

### **3.3 在PyTorch中的可操作实现**

PyTorch的 torch.nn.utils.parametrize 提供了一个强大而优雅的框架，用于将自定义参数化应用于模型的参数，而无需重写整个层。这使得实现凯莱变换约束变得非常直接 63。

以下是实现这一过程的详细步骤和概念代码：

**步骤1：定义斜埃尔米特参数化模块**

首先，创建一个 nn.Module，它接收一个任意的复数方阵，并将其投影到斜埃尔米特矩阵空间。

Python

import torch  
import torch.nn as nn  
import torch.nn.utils.parametrize as parametrize

class SkewHermitian(nn.Module):  
    def forward(self, X):  
        \# A \= 0.5 \* (X \- X.conj().T)  
        return 0.5 \* (X \- X.conj().transpose(-2, \-1))

    \# right\_inverse is optional but good practice for initialization  
    def right\_inverse(self, A):  
        \# Assuming A is skew-hermitian, A is a fixed point of this projection  
        return A

**步骤2：定义凯莱变换模块**

接下来，创建实现缩放凯莱变换的 nn.Module。为了简化，这里展示不带缩放矩阵 D 的版本，但在实际应用中可以轻松加入。

Python

class CayleyMap(nn.Module):  
    def \_\_init\_\_(self, size):  
        super().\_\_init\_\_()  
        \# Register Identity matrix as a buffer, not a parameter  
        self.register\_buffer("Id", torch.eye(size, dtype=torch.complex128))

    def forward(self, X\_skew):  
        \# Input X\_skew is assumed to be a skew-hermitian matrix  
        \# W \= (I \- X\_skew) @ torch.linalg.inv(I \+ X\_skew)  
        \# Using torch.linalg.solve for better numerical stability and efficiency  
        \# Solves (I \+ X\_skew) \* W \= (I \- X\_skew) for W  
        return torch.linalg.solve(self.Id \+ X\_skew, self.Id \- X\_skew)

注意，这里使用了 torch.linalg.solve(A, B) 来求解线性方程组 AW=B，这在数值上比直接计算 torch.linalg.inv(A) @ B 更稳定。

**步骤3：在解码器输出层上注册参数化**

在CS-UNet解码器的末端，为生成 U 和 V 前驱体的线性层（或卷积层）的权重或输出上注册这两个参数化模块。假设解码器有两个分支，分别输出大小为 M×R 和 N×R 的矩阵前驱体。

Python

\# Assuming decoder\_u\_branch and decoder\_v\_branch are the final layers  
\# producing precursors for U and V.  
\# Let's assume they output square matrices for simplicity of Cayley transform.  
\# The network would learn a square matrix, from which we take the first R columns.  
\# Or, more advanced techniques for non-square Stiefel manifolds exist.

\# For a simplified example on a linear layer:  
u\_output\_layer \= nn.Linear(in\_features, M \* M) \# Outputs a flattened M x M matrix precursor  
v\_output\_layer \= nn.Linear(in\_features, N \* N) \# Outputs a flattened N x N matrix precursor

\# In the model's forward pass, reshape the output  
\# x\_u \= u\_output\_layer(features).view(-1, M, M)  
\#... then apply the parametrization.

\# A more direct way is to create a parameter and parametrize it.  
class SVDHead(nn.Module):  
    def \_\_init\_\_(self, M, N, R):  
        super().\_\_init\_\_()  
        \# Create unconstrained parameters that the network will learn to produce  
        self.u\_precursor \= nn.Parameter(torch.randn(M, M, dtype=torch.complex128))  
        self.v\_precursor \= nn.Parameter(torch.randn(N, N, dtype=torch.complex128))  
        self.s\_precursor \= nn.Parameter(torch.randn(R))

        \# Apply parametrizations  
        parametrize.register\_parametrization(self, "u\_precursor", SkewHermitian())  
        parametrize.register\_parametrization(self, "u\_precursor", CayleyMap(M))  
          
        parametrize.register\_parametrization(self, "v\_precursor", SkewHermitian())  
        parametrize.register\_parametrization(self, "v\_precursor", CayleyMap(N))

    def forward(self, features): \# features from the decoder backbone  
        \# In a real implementation, the precursors would be dynamically generated  
        \# by linear layers taking 'features' as input.  
        \# This example shows the parametrization on a static parameter.  
          
        \# The parametrized attributes are now orthogonal  
        U\_full \= self.u\_precursor   
        V\_full \= self.v\_precursor  
          
        \# Take the first R columns as specified by the task  
        U \= U\_full  
        V \= V\_full  
          
        \# Singular values must be non-negative, apply a ReLU or Softplus  
        s \= torch.nn.functional.relu(self.s\_precursor)  
          
        return U, s, V

通过依次注册 SkewHermitian 和 CayleyMap，PyTorch会自动将它们串联起来。网络在反向传播时，会通过这个可微的变换链，正确地更新最原始的、无约束的参数。

### **3.4 数值稳定性考量**

凯莱变换中的矩阵求逆步骤对数值精度非常敏感，尤其是在 (I+A) 接近奇异时。在标准的32位浮点数（float32）精度下，微小的舍入误差可能导致最终输出的 U 和 V 并非严格的酉矩阵，从而使得AE度量中的酉矩阵惩罚项不为零，影响最终得分。

**使用双精度（float64）**

为了最大程度地减小数值误差，强烈建议在模型的最后几层、凯莱变换模块以及损失计算中使用双精度浮点数（torch.double 或 float64）64。

* **实现**：可以通过调用 .to(torch.double) 或 .double() 方法将相关的张量和模块转换为双精度。  
* **优势**：双精度提供了更高的数值表示范围和精度，可以显著降低矩阵求逆过程中的误差累积，使得计算出的 U∗U 和 V∗V 更接近于理想的单位矩阵，从而将AE中的惩罚项最小化至机器精度水平。  
* **权衡**：使用双精度会带来大约两倍的内存消耗和计算开销。然而，考虑到这一操作仅限于模型的最后部分，并且对于最小化AE至关重要，这种额外的开销是完全合理且必要的。在追求极致性能的竞赛环境中，这种对数值稳定性的关注往往是区分顶尖方案的关键。

## **第4节：实现性能与效率最优化的教师-学生框架**

在解决了模型架构和酉矩阵约束的核心技术问题后，下一个挑战是如何在竞赛设定的双重目标——极低的近似误差（AE）和高效的计算复杂度（乘加次数）——之间取得最佳平衡。一个单一的模型很难同时在这两个维度上都达到顶峰。一个更大、更复杂的模型（如我们提出的CS-UNet）可能在AE上表现出色，但计算成本高昂；而一个轻量级模型虽然高效，但可能无法达到最低的AE。

为了系统性地解决这一困境，我们引入一个三阶段的“教师-学生”训练与推理框架。这个框架通过解耦追求极致精度和追求极致效率的过程，最终将两者的优势融合到一个单一、高效且高性能的模型中。该框架包括：(1) 使用快照集成（Snapshot Ensembling）训练一个强大的教师模型集合；(2) 通过知识蒸馏（Knowledge Distillation）将集成模型的知识迁移到一个轻量级的学生模型中；(3) 在最终推理阶段使用测试时增强（Test-Time Augmentation）进一步提升学生模型的鲁棒性和准确性。

### **4.2 阶段一：使用快照集成训练教师模型**

**目标**：此阶段的唯一目标是创建一个或多个“教师”模型，这些模型能够产生尽可能准确和鲁棒的SVD预测，暂时不考虑其推理时的计算成本。这些高质量的预测将作为下一阶段训练学生模型的“软标签”。

**技术：快照集成（Snapshot Ensembling）**

传统集成学习需要独立训练多个模型，计算成本高昂。快照集成是一种极其高效的技术，它通过在一次单独的训练过程中，利用周期性的学习率调度策略，使模型收敛到多个不同的优质局部最小值，从而免费获得一个模型集成 65。

* **核心机制**：采用周期性的余弦退火学习率（Cyclic Cosine Annealing）。在一个训练周期内，学习率从一个较高的初始值平滑地下降到接近零。当学习率很高时，优化器（如SGD或Adam）有足够的动能探索参数空间，跳出现有的局部最小值；当学习率降低时，优化器则会稳定地收敛到附近的一个新的局部最小值。  
* **实现**：  
  * **教师架构**：采用第2节中提出的、性能强大的混合架构CS-UNet。  
  * **训练过程**：进行一次完整的、较长时间的训练。例如，总共训练300个周期（epochs）。可以将学习率调度器设置为包含 M=5 个循环，每个循环持续60个周期。在每个循环结束时（即第60、120、180、240、300个周期），当学习率最低、模型最稳定时，保存一次模型的权重。这样，一次训练就能产生5个性能强大且存在差异性的模型，构成一个集成。

**集成预测与标签生成**

在获得了 M 个教师模型快照后，我们将使用整个训练数据集来生成用于蒸馏的软标签。

* **预测**：对于每一个训练样本（非理想信道 H），我们分别用 M 个教师模型进行预测，得到 M 组SVD分量 {(U1​,s1​,V1​),(U2​,s2​,V2​),…,(UM​,sM​,VM​)}。  
* **平均化**：由于输出是结构化的，简单的逐元素平均可能不是最优选择，尤其对于酉矩阵 U 和 V。一个更具原则性的方法是：  
  1. 对于奇异值向量 s，可以直接计算算术平均值：steacher​=M1​∑i=1M​si​。  
  2. 对于酉矩阵 U 和 V，它们位于斯蒂费尔流形上，直接在欧几里得空间中求平均会破坏其酉矩阵性质。正确的做法是先计算平均矩阵（例如 Uˉ=M1​∑i=1M​Ui​），然后将这个平均矩阵重新投影回斯蒂费尔流形上。一个简单而有效的方法是对平均矩阵进行一次SVD或QR分解，取其正交/酉分量作为最终的教师标签 71。例如，  
     Uteacher​=proj(Uˉ)。

通过这种方式生成的教师标签 (Uteacher​,steacher​,Vteacher​) 相比任何单个模型的预测都更加稳定和准确，因为它平均掉了单个模型的随机偏差和过拟合效应，提供了更平滑、更具泛化性的目标。

### **4.3 阶段二：将知识蒸馏到轻量级学生模型**

**目标**：训练一个参数量少、计算速度快（低乘加次数）的“学生”模型，使其能够模拟并达到强大的教师集成的性能水平。

**技术：知识蒸馏（Knowledge Distillation）**

知识蒸馏是一种模型压缩技术，其核心思想是让学生模型学习教师模型（或集成）的输出，而不是直接学习硬性的真实标签 83。教师模型的“软”输出包含了其对数据结构和类别关系的丰富理解（即所谓的“暗知识”），这为学生模型的学习提供了比稀疏的真实标签更有价值的监督信号。

* **学生架构**：选择一个轻量级的纯ConvNeXt-UNet架构。该架构在保持U-Net结构优势的同时，利用ConvNeXt块实现了卓越的效率-性能平衡 42。同样，该学生模型也必须集成第3节中描述的凯莱变换输出层，以保证其输出的酉矩阵约束。  
* **蒸馏过程**：  
  * **输入**：学生模型使用与教师模型相同的原始训练输入（非理想信道 H）。  
  * **目标**：学生模型的训练目标是最小化其预测 (Ustudent​,sstudent​,Vstudent​) 与上一阶段生成的教师集成标签 (Uteacher​,steacher​,Vteacher​) 之间的差异。  
  * **蒸馏损失函数**：这是一种基于响应的知识蒸馏（Response-Based KD），非常适用于回归和结构化预测任务 83。损失函数可以定义为各分量之间的均方误差（MSE）或平均绝对误差（L1 Loss）之和：

Ldistill​=λU​∥Ustudent​−Uteacher​∥F2​+λs​∥sstudent​−steacher​∥22​+λV​∥Vstudent​−Vteacher​∥F2​其中 λU​,λs​,λV​ 是用于平衡不同分量重要性的权重超参数。

通过这个过程，学生模型被引导去学习教师集成所捕获的从输入到输出的平滑、泛化的函数映射，而不仅仅是拟合训练数据。这使得一个更小、更高效的模型能够达到甚至超越一个独立训练的大模型的性能。

### **4.4 阶段三：使用测试时增强（TTA）进行最终推理**

**目标**：在提交模型进行最终评测时，从已经训练好的、高效的学生模型中榨取最后一点性能提升，特别是增强其在面对未知测试数据时的鲁棒性。

**技术：测试时增强（Test-Time Augmentation, TTA）**

TTA是一种在推理阶段应用的简单而有效的技术。它通过对单个测试样本进行多次数据增强，分别进行预测，然后将多次预测结果进行平均，从而得到一个更稳定、更准确的最终结果 98。

* **核心机制**：TTA利用了这样一个事实：即使是微小的数据扰动也可能导致模型预测发生变化。通过对这些变化的预测进行平均，可以有效地平滑模型的决策边界，减少由于输入数据的微小、不相关变异而导致的预测噪声。  
* **实现**：  
  * **输入增强**：对于每一个待测试的非理想信道矩阵 Htest​，生成一个小的增强集合。由于信道数据是复数矩阵，增强可以包括：  
    1. **原始输入**：Htest​ 本身。  
    2. **噪声注入**：向 Htest​ 添加不同实例的、低能量的高斯白噪声。  
    3. **相位旋转**：将整个矩阵乘以一个随机的单位模长复数 ejθ。  
  * **预测与平均**：将这个增强集合中的每一个矩阵都输入到训练好的学生模型中，得到多组SVD预测。然后，使用与4.2节中相同的流形感知平均方法，对这些预测进行平均和重投影，得到最终的SVD结果 (Ufinal​,sfinal​,Vfinal​)。

这个三阶段的流程是一个系统性的解决方案，它巧妙地将对极致准确性的追求（通过教师集成）与对高效推理的需求（通过轻量级学生模型）分离开来，并通过知识蒸馏将前者赋予后者。最后的TTA步骤则为这个高效模型提供了额外的鲁棒性保障，使其在面对多样化和充满非理想因素的真实测试环境时表现更佳。

## **第5节：综合方案与战略建议**

本节将前述所有分析和技术选择综合成一个连贯的、可执行的解决方案蓝图。该蓝图旨在为参赛者提供一个从数据准备到最终提交的清晰路线图，并辅以关于超参数优化和战略性思考的建议，以确保在竞赛中取得领先地位。

### **5.1 集成解决方案蓝图**

以下是实施本报告所提出策略的详细分步工作流程。

**步骤1：数据准备与基准建立**

* **数据预处理**：对组委会提供的所有训练数据（RoundYTrainDataX.npy）和标签（RoundYTrainLabelX.npy）进行标准化处理。考虑到输入是复数信道矩阵，可以考虑将实部和虚部分别进行零均值和单位方差的归一化。  
* **生成基准标签**：虽然在训练学生模型时将使用教师模型的软标签，但为了验证和监控教师模型的训练过程，需要一个高质量的基准。使用高精度数值计算库（如NumPy或SciPy，并确保使用float64精度）对所有RoundYTrainLabelX.npy理想信道数据进行SVD分解，生成基准的 (Ulabel​,slabel​,Vlabel​)。这是评估教师模型性能的黄金标准 1。

**步骤2：教师模型集成训练**

* **模型选择**：实现第2.3节中描述的混合CS-UNet架构。确保瓶颈层使用Swin Transformer块，编码器主体使用ConvNeXt块，并且解码器的输出层连接到第3.3节中实现的凯莱变换参数化模块。  
* **训练策略**：采用第4.2节详述的快照集成方法。配置一个周期性的余弦退火学习率调度器。例如，如果总训练周期为300，设置5个循环，每个循环60个周期。  
* **模型保存**：在每个学习率循环的末尾（即学习率最低点），保存模型的权重。这将产生 M 个（例如5个）性能强大且具有多样性的教师模型快照。

**步骤3：生成蒸馏软标签**

* **集成推理**：将整个训练集的非理想信道数据输入到步骤2中保存的 M 个教师模型快照中，得到 M 组预测结果。  
* **标签聚合**：按照第4.2节描述的流形感知平均方法，对这 M 组预测进行聚合。对奇异值向量 s 进行算术平均；对酉矩阵 U 和 V 进行算术平均后，通过SVD或QR分解重新投影到斯蒂费尔流形上。将聚合后的结果 (Uteacher​,steacher​,Vteacher​) 保存为新的训练标签集。

**步骤4：学生模型知识蒸馏训练**

* **模型选择**：实现一个轻量级的、纯ConvNeXt-UNet架构。该模型同样必须集成凯莱变换参数化模块以保证输出的酉矩阵约束。其参数量和计算复杂度应远低于教师模型，以满足竞赛的效率指标要求。  
* **训练过程**：使用原始的非理想信道数据作为输入，但使用步骤3中生成的蒸馏软标签作为训练目标。  
* **损失函数**：采用第4.3节中定义的蒸馏损失函数 Ldistill​。使用标准的优化器（如AdamW）和学习率调度策略（如Cosine Annealing，但这次是单次下降）进行训练，直至收敛。

**步骤5：打包与提交**

* **最终模型**：步骤4中训练完成的学生模型是最终需要提交的模型。  
* **推理代码**：在 solution.py 的推理逻辑中，实现第4.4节描述的测试时增强（TTA）流程。即，对每个测试样本生成多个增强版本，分别送入学生模型进行预测，然后对预测结果进行流形感知的平均，得到最终输出。  
* **打包文件**：根据竞赛要求，将训练好的学生模型参数文件（.pth）、模型定义文件 solution.py 以及在所有测试数据集上运行TTA后生成的输出结果文件（RoundYTestOutputX.npz）打包为.zip文件进行提交 1。

下表2将整个解决方案流程进行了可视化总结，清晰地展示了每个阶段的目标、方法和产出。

The following table:

**表2：最终解决方案流程管道**

| 阶段 | 目标 | 模型架构 | 关键技术 | 产出 |
| :---- | :---- | :---- | :---- | :---- |
| **1\. 教师训练** | 最大化SVD近似精度，生成高质量的预测器 | 混合CS-UNet | 快照集成 (Snapshot Ensembling) | M个高性能的教师模型快照 |
| **2\. 蒸馏标签生成** | 创建稳定、平滑、高泛化性的训练目标 | M模型集成 | 预测结果的流形感知平均与重投影 | 用于学生模型训练的蒸馏标签集 (U,s,V)teacher​ |
| **3\. 学生训练** | 将教师知识迁移至一个高效、轻量级的模型 | 轻量级ConvNeXt-UNet | 知识蒸馏 (Knowledge Distillation) | 训练完成的、满足效率要求的最终学生模型 |
| **4\. 最终推理** | 最大化最终提交结果的鲁棒性和分数 | 训练好的学生模型 | 测试时增强 (Test-Time Augmentation, TTA) | 最终提交的 .npz 结果文件 |

### **5.2 超参数优化与消融研究的结构化方法**

为了验证本方案中各个组件的有效性并找到最优配置，进行系统性的超参数优化和消融研究是必不可少的。

* **超参数优化**：  
  * **学习率调度**：对于教师模型的快照集成，关键参数是初始最大学习率、每个循环的周期数以及总循环数 M。对于学生模型的蒸馏，需要调整其初始学习率和总训练周期。  
  * **蒸馏损失权重**：λU​,λs​,λV​ 的相对大小需要调整，以确定模型应更关注奇异矩阵还是奇异值的匹配精度。  
  * **TTA策略**：需要实验不同的增强方法（如噪声水平、相位旋转范围）和增强样本的数量，以在性能提升和推理时间增加之间找到最佳平衡点。  
* **消融研究**：为了证明每个创新点的贡献，建议进行以下对比实验：  
  1. **架构对比**：训练一个纯ConvNeXt-UNet和一个纯Swin-UNet，与混合CS-UNet教师模型的性能进行比较。  
  2. **约束方法对比**：将使用凯莱变换的模型与一个不使用凯莱变换、仅在损失函数中加入酉矩阵惩罚项的模型进行比较，以量化几何方法的优势。  
  3. **训练框架对比**：将通过知识蒸馏训练的学生模型，与一个同样架构但直接在真实标签上训练的模型进行比较，以验证蒸馏的效果。  
  4. **推理方法对比**：提交一次使用TTA的结果和一次不使用TTA的结果，直接比较TTA带来的分数提升。

### **5.3 最终战略建议**

1. **优先确保酉矩阵约束的实现**：在所有技术点中，通过凯莱变换实现酉矩阵约束是理论上对降低AE最直接、最关键的一步。应优先投入精力确保该模块的正确、高效和数值稳定的实现。  
2. **充分利用教师-学生框架的解耦优势**：不要试图用一个模型同时解决所有问题。大胆地为教师模型使用更深、更宽的网络结构，甚至更长的训练时间，因为它的成本是一次性的。然后，将重点放在如何高效地将这些知识压缩到满足竞赛效率要求的学生模型中。  
3. **遵守竞赛规则与流程**：严格遵循组委会提供的代码结构（如solution.py中的SVDNet类名）、输入输出维度、文件命名和打包要求，避免因格式错误导致评测失败 1。  
4. **策略性利用提交机会**：每天5次的提交机会非常宝贵。可以利用这些机会进行快速的超参数验证或消融研究。例如，在一天内提交使用不同TTA策略的版本，以快速找到最优的推理配置。始终以最后一次提交的成绩为准，这允许在早期进行探索性提交，在截止日期前提交最优方案 1。

通过遵循这一系统化、多层次的战略蓝图，参赛者将能够构建一个在理论上合理、在技术上先进、在实践中高效的解决方案，从而在激烈竞争中脱颖而出，实现极致降低近似误差（AE）的最终目标。

#### **引用的著作**

1. QA.docx  
2. U-Nets with ResNet Encoders and cross connections | by Chris ..., 访问时间为 七月 29, 2025， [https://medium.com/data-science/u-nets-with-resnet-encoders-and-cross-connections-d8ba94125a2c](https://medium.com/data-science/u-nets-with-resnet-encoders-and-cross-connections-d8ba94125a2c)  
3. What is the use of BackBone in UNET model ? | Kaggle, 访问时间为 七月 29, 2025， [https://www.kaggle.com/general/205141](https://www.kaggle.com/general/205141)  
4. mkisantal/backboned-unet: Build U-Nets for segmentation from pre-trained TorchVision models. \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/mkisantal/backboned-unet](https://github.com/mkisantal/backboned-unet)  
5. A ConvNet for the 2020s \- CVF Open Access, 访问时间为 七月 29, 2025， [https://openaccess.thecvf.com/content/CVPR2022/papers/Liu\_A\_ConvNet\_for\_the\_2020s\_CVPR\_2022\_paper.pdf](https://openaccess.thecvf.com/content/CVPR2022/papers/Liu_A_ConvNet_for_the_2020s_CVPR_2022_paper.pdf)  
6. A ConvNet for the 2020s \- Mansi Kataria \- Medium, 访问时间为 七月 29, 2025， [https://zoomout.medium.com/a-convnet-for-the-2020s-aa74632b4c2](https://zoomout.medium.com/a-convnet-for-the-2020s-aa74632b4c2)  
7. A ConvNet for the 2020s | Request PDF \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/publication/363907060\_A\_ConvNet\_for\_the\_2020s](https://www.researchgate.net/publication/363907060_A_ConvNet_for_the_2020s)  
8. This repo is for an implementation of the prestigious research paper "A ConvNet of the 2020s" published by Zhuang Liu, Hanzi Mao, Chao-Yuan Wu, Christoph Feichtenhofer, Trevor Darrell, Saining Xie, Facebook AI Research (FAIR), UC Berkeley on the 10 January 2022 \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/AlassaneSakande/A-ConvNet-of-2020s](https://github.com/AlassaneSakande/A-ConvNet-of-2020s)  
9. \[2201.03545\] A ConvNet for the 2020s \- ar5iv \- arXiv, 访问时间为 七月 29, 2025， [https://ar5iv.labs.arxiv.org/html/2201.03545](https://ar5iv.labs.arxiv.org/html/2201.03545)  
10. CVPR Poster ConvNeXt V2: Co-Designing and Scaling ConvNets With Masked Autoencoders, 访问时间为 七月 30, 2025， [https://cvpr.thecvf.com/virtual/2023/poster/22892](https://cvpr.thecvf.com/virtual/2023/poster/22892)  
11. A Lightweight Convolutional Neural Network with Hierarchical Multi-Scale Feature Fusion for Image Classification \- Scirp.org., 访问时间为 七月 29, 2025， [https://www.scirp.org/journal/paperinformation?paperid=131543](https://www.scirp.org/journal/paperinformation?paperid=131543)  
12. Depthwise Separable Convolutions with Deep Residual Convolutions \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/html/2411.07544v1](https://arxiv.org/html/2411.07544v1)  
13. \[1909.11321\] FALCON: Lightweight and Accurate Convolution \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/abs/1909.11321](https://arxiv.org/abs/1909.11321)  
14. a lightweight and efficient deep convolutional neural network based on depthwise dilated separ \- Jatit.org, 访问时间为 七月 29, 2025， [http://www.jatit.org/volumes/Vol98No15/5Vol98No15.pdf](http://www.jatit.org/volumes/Vol98No15/5Vol98No15.pdf)  
15. Falcon: lightweight and accurate convolution based on depthwise separable convolution, 访问时间为 七月 29, 2025， [https://snu.elsevierpure.com/en/publications/falcon-lightweight-and-accurate-convolution-based-on-depthwise-se](https://snu.elsevierpure.com/en/publications/falcon-lightweight-and-accurate-convolution-based-on-depthwise-se)  
16. A novel lightweight parallel depthwise separable convolutional neural... \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/figure/A-novel-lightweight-parallel-depthwise-separable-convolutional-neural-network-PDS-CNN\_fig4\_387905458](https://www.researchgate.net/figure/A-novel-lightweight-parallel-depthwise-separable-convolutional-neural-network-PDS-CNN_fig4_387905458)  
17. This is an official implementation for "Swin Transformer: Hierarchical Vision Transformer using Shifted Windows". \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/microsoft/Swin-Transformer](https://github.com/microsoft/Swin-Transformer)  
18. SwinIR: Image Restoration Using Swin Transformer \- CVF Open Access, 访问时间为 七月 29, 2025， [https://openaccess.thecvf.com/content/ICCV2021W/AIM/papers/Liang\_SwinIR\_Image\_Restoration\_Using\_Swin\_Transformer\_ICCVW\_2021\_paper.pdf](https://openaccess.thecvf.com/content/ICCV2021W/AIM/papers/Liang_SwinIR_Image_Restoration_Using_Swin_Transformer_ICCVW_2021_paper.pdf)  
19. skchen1993/SwinIR: experiment for reproducing SwinIR result \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/skchen1993/SwinIR](https://github.com/skchen1993/SwinIR)  
20. JingyunLiang/SwinIR: SwinIR: Image Restoration Using ... \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/JingyunLiang/SwinIR](https://github.com/JingyunLiang/SwinIR)  
21. This is an official implementation for "Video Swin Transformers". \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/SwinTransformer/Video-Swin-Transformer](https://github.com/SwinTransformer/Video-Swin-Transformer)  
22. koechslin/Swin-Transformer-Semantic-Segmentation \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/koechslin/Swin-Transformer-Semantic-Segmentation](https://github.com/koechslin/Swin-Transformer-Semantic-Segmentation)  
23. Swin Transformer \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/SwinTransformer](https://github.com/SwinTransformer)  
24. Swin-Transformer-based Unet architecture for semantic segmentation with Pytorch code. | by Ashish bisht | Medium, 访问时间为 七月 29, 2025， [https://medium.com/@ashishbisht0307/swin-transformer-based-unet-architecture-for-semantic-segmentation-with-pytorch-code-91e779334e8e](https://medium.com/@ashishbisht0307/swin-transformer-based-unet-architecture-for-semantic-segmentation-with-pytorch-code-91e779334e8e)  
25. Swin–UNet++: A Nested Swin Transformer Architecture for Location Identification and Morphology Segmentation of Dimples on 2.25Cr1Mo0.25V Fractured Surface, 访问时间为 七月 29, 2025， [https://pmc.ncbi.nlm.nih.gov/articles/PMC8703304/](https://pmc.ncbi.nlm.nih.gov/articles/PMC8703304/)  
26. STM-UNet: An Efficient U-shaped Architecture Based on Swin Transformer and Multi-scale MLP for Medical Image Segmentation \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/pdf/2304.12615](https://arxiv.org/pdf/2304.12615)  
27. Swin-Unet model architecture \[8\] | Download Scientific Diagram \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/figure/Swin-Unet-model-architecture-8\_fig1\_371148423](https://www.researchgate.net/figure/Swin-Unet-model-architecture-8_fig1_371148423)  
28. Enhancing medical image segmentation with a multi-transformer U-Net \- PMC, 访问时间为 七月 29, 2025， [https://pmc.ncbi.nlm.nih.gov/articles/PMC10909362/](https://pmc.ncbi.nlm.nih.gov/articles/PMC10909362/)  
29. A Hybrid Learnable Fusion of ConvNeXt and Swin Transformer for Optimized Image Classification \- MDPI, 访问时间为 七月 30, 2025， [https://www.mdpi.com/2624-831X/6/2/30](https://www.mdpi.com/2624-831X/6/2/30)  
30. Hybrid U-Net Model with Visual Transformers for Enhanced Multi-Organ Medical Image Segmentation \- MDPI, 访问时间为 七月 30, 2025， [https://www.mdpi.com/2078-2489/16/2/111](https://www.mdpi.com/2078-2489/16/2/111)  
31. FNeXter: A Multi-Scale Feature Fusion Network Based on ConvNeXt ..., 访问时间为 七月 30, 2025， [https://pmc.ncbi.nlm.nih.gov/articles/PMC11054479/](https://pmc.ncbi.nlm.nih.gov/articles/PMC11054479/)  
32. Medical Image Segmentation Review: The Success of U-Net \- IEEE Computer Society, 访问时间为 七月 30, 2025， [https://www.computer.org/csdl/journal/tp/2024/12/10643318/1ZAxlZmCbDi](https://www.computer.org/csdl/journal/tp/2024/12/10643318/1ZAxlZmCbDi)  
33. Next-Gen Medical Imaging: U-Net Evolution and the Rise of Transformers \- PubMed Central, 访问时间为 七月 30, 2025， [https://pmc.ncbi.nlm.nih.gov/articles/PMC11280776/](https://pmc.ncbi.nlm.nih.gov/articles/PMC11280776/)  
34. Hybrid UNet transformer architecture for ischemic stoke segmentation with MRI and CT datasets \- Frontiers, 访问时间为 七月 30, 2025， [https://www.frontiersin.org/journals/neuroscience/articles/10.3389/fnins.2023.1298514/full](https://www.frontiersin.org/journals/neuroscience/articles/10.3389/fnins.2023.1298514/full)  
35. Uformer: A General U-Shaped Transformer for ... \- CVF Open Access, 访问时间为 七月 30, 2025， [https://openaccess.thecvf.com/content/CVPR2022/papers/Wang\_Uformer\_A\_General\_U-Shaped\_Transformer\_for\_Image\_Restoration\_CVPR\_2022\_paper.pdf](https://openaccess.thecvf.com/content/CVPR2022/papers/Wang_Uformer_A_General_U-Shaped_Transformer_for_Image_Restoration_CVPR_2022_paper.pdf)  
36. EViT-Unet: U-Net Like Efficient Vision Transformer for Medical Image Segmentation on Mobile and Edge Devices \- arXiv, 访问时间为 七月 30, 2025， [https://arxiv.org/html/2410.15036v1](https://arxiv.org/html/2410.15036v1)  
37. Combining Transformers and CNNs for Efficient Object Detection in High-Resolution Satellite Imagery \- arXiv, 访问时间为 七月 30, 2025， [https://arxiv.org/html/2507.11040v1](https://arxiv.org/html/2507.11040v1)  
38. (PDF) Combining Transformers and CNNs for Efficient Object Detection in High-Resolution Satellite Imagery \- ResearchGate, 访问时间为 七月 30, 2025， [https://www.researchgate.net/publication/393724090\_Combining\_Transformers\_and\_CNNs\_for\_Efficient\_Object\_Detection\_in\_High-Resolution\_Satellite\_Imagery](https://www.researchgate.net/publication/393724090_Combining_Transformers_and_CNNs_for_Efficient_Object_Detection_in_High-Resolution_Satellite_Imagery)  
39. Combining CNN and Transformer as Encoder to Improve End-to-End Handwritten Mathematical Expression Recognition Accuracy \- ResearchGate, 访问时间为 七月 30, 2025， [https://www.researchgate.net/publication/365734389\_Combining\_CNN\_and\_Transformer\_as\_Encoder\_to\_Improve\_End-to-End\_Handwritten\_Mathematical\_Expression\_Recognition\_Accuracy](https://www.researchgate.net/publication/365734389_Combining_CNN_and_Transformer_as_Encoder_to_Improve_End-to-End_Handwritten_Mathematical_Expression_Recognition_Accuracy)  
40. convformer: combining cnn and transformer for medical image segmentation \- University of Notre Dame, 访问时间为 七月 30, 2025， [https://www3.nd.edu/\~cwang11/research/isbi23-convformer.pdf](https://www3.nd.edu/~cwang11/research/isbi23-convformer.pdf)  
41. Better computer vision models by combining Transformers and convolutional neural networks \- Meta AI, 访问时间为 七月 30, 2025， [https://ai.meta.com/blog/computer-vision-combining-transformers-and-convolutional-neural-networks/](https://ai.meta.com/blog/computer-vision-combining-transformers-and-convolutional-neural-networks/)  
42. Lightweight Deep Learning Model, ConvNeXt-U: An Improved U-Net Network for Extracting Cropland in Complex Landscapes from Gaofen-2 Images \- PubMed, 访问时间为 七月 29, 2025， [https://pubmed.ncbi.nlm.nih.gov/39797051/](https://pubmed.ncbi.nlm.nih.gov/39797051/)  
43. ConvNeXt embedded U-Net for semantic segmentation in urban scenes of multi-scale targets \- R Discovery, 访问时间为 七月 29, 2025， [https://discovery.researcher.life/article/convnext-embedded-unet-for-semantic-segmentation-in-urban-scenes-of-multiscale-targets/8125a760f8ca3c8d873e3d163d634ebe](https://discovery.researcher.life/article/convnext-embedded-unet-for-semantic-segmentation-in-urban-scenes-of-multiscale-targets/8125a760f8ca3c8d873e3d163d634ebe)  
44. ConvNeXt-UperNet-Based Deep Learning Model for Road Extraction from High-Resolution Remote Sensing Images \- SciOpen, 访问时间为 七月 29, 2025， [https://www.sciopen.com/article/10.32604/cmc.2024.052597](https://www.sciopen.com/article/10.32604/cmc.2024.052597)  
45. ConvNeXt: A Transformer-Inspired CNN Architecture | KUNGFU.AI Blog, 访问时间为 七月 29, 2025， [https://www.kungfu.ai/blog-post/convnext-a-transformer-inspired-cnn-architecture](https://www.kungfu.ai/blog-post/convnext-a-transformer-inspired-cnn-architecture)  
46. Lightweight Deep Learning Model, ConvNeXt-U: An Improved U-Net Network for Extracting Cropland in Complex Landscapes from Gaofen-2 Images \- MDPI, 访问时间为 七月 29, 2025， [https://www.mdpi.com/1424-8220/25/1/261](https://www.mdpi.com/1424-8220/25/1/261)  
47. CI-UNet: melding convnext and cross-dimensional attention for robust medical image segmentation \- PMC, 访问时间为 七月 29, 2025， [https://pmc.ncbi.nlm.nih.gov/articles/PMC10874369/](https://pmc.ncbi.nlm.nih.gov/articles/PMC10874369/)  
48. UNet Architecture with ConvNext computational blocks offers superior... | Download Scientific Diagram \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/figure/UNet-Architecture-with-ConvNext-computational-blocks-offers-superior-accuracy-per\_fig2\_370621145](https://www.researchgate.net/figure/UNet-Architecture-with-ConvNext-computational-blocks-offers-superior-accuracy-per_fig2_370621145)  
49. Real-Time ConvNext-Based U-Net with Feature Infusion for Egg Microcrack Detection, 访问时间为 七月 29, 2025， [https://www.mdpi.com/2077-0472/14/9/1655](https://www.mdpi.com/2077-0472/14/9/1655)  
50. 访问时间为 一月 1, 1970， [https://arxiv.org/pdf/2105.05537](https://arxiv.org/pdf/2105.05537)  
51. arXiv:2010.11929v2 \[cs.CV\] 3 Jun 2021, 访问时间为 七月 29, 2025， [https://arxiv.org/pdf/2010.11929](https://arxiv.org/pdf/2010.11929)  
52. Coordinate descent on the Stiefel manifold for deep neural network training, 访问时间为 七月 29, 2025， [https://www.esann.org/sites/default/files/proceedings/2023/ES2023-143.pdf](https://www.esann.org/sites/default/files/proceedings/2023/ES2023-143.pdf)  
53. Decentralized Riemannian Gradient Descent on the Stiefel Manifold, 访问时间为 七月 29, 2025， [https://proceedings.mlr.press/v139/chen21g.html](https://proceedings.mlr.press/v139/chen21g.html)  
54. Feedback Gradient Descent: Efficient and Stable Optimization with Orthogonality for DNNs, 访问时间为 七月 30, 2025， [https://ojs.aaai.org/index.php/AAAI/article/view/20558/20317](https://ojs.aaai.org/index.php/AAAI/article/view/20558/20317)  
55. Orthogonal Recurrent Neural Networks with Scaled Cayley Transform | Request PDF, 访问时间为 七月 30, 2025， [https://www.researchgate.net/publication/327793425\_Orthogonal\_Recurrent\_Neural\_Networks\_with\_Scaled\_Cayley\_Transform](https://www.researchgate.net/publication/327793425_Orthogonal_Recurrent_Neural_Networks_with_Scaled_Cayley_Transform)  
56. Supplementary Information, 访问时间为 七月 29, 2025， [https://proceedings.neurips.cc/paper\_files/paper/2023/file/6ac807c9b296964409b277369e55621a-Supplemental-Conference.pdf](https://proceedings.neurips.cc/paper_files/paper/2023/file/6ac807c9b296964409b277369e55621a-Supplemental-Conference.pdf)  
57. Orthogonalizing Convolutional Layers with the Cayley Transform \- OpenReview, 访问时间为 七月 30, 2025， [https://openreview.net/pdf?id=Pbj8H\_jEHYv](https://openreview.net/pdf?id=Pbj8H_jEHYv)  
58. Cheap Orthogonal Constraints in Neural Networks: A Simple Parametrization of the Orthogonal and Unitary Group \- Proceedings of Machine Learning Research, 访问时间为 七月 29, 2025， [http://proceedings.mlr.press/v97/lezcano-casado19a/lezcano-casado19a.pdf](http://proceedings.mlr.press/v97/lezcano-casado19a/lezcano-casado19a.pdf)  
59. Cayley transform \- Wikipedia, 访问时间为 七月 29, 2025， [https://en.wikipedia.org/wiki/Cayley\_transform](https://en.wikipedia.org/wiki/Cayley_transform)  
60. Orthogonal Recurrent Neural Networks with Scaled Cayley Transform, 访问时间为 七月 29, 2025， [http://proceedings.mlr.press/v80/helfrich18a/helfrich18a.pdf](http://proceedings.mlr.press/v80/helfrich18a/helfrich18a.pdf)  
61. Orthogonal Recurrent Neural Networks with Scaled Cayley ... \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/pdf/1707.09520](https://arxiv.org/pdf/1707.09520)  
62. Complex Unitary Recurrent Neural Networks Using Scaled Cayley Transform, 访问时间为 七月 30, 2025， [https://ojs.aaai.org/index.php/AAAI/article/view/4371/4249](https://ojs.aaai.org/index.php/AAAI/article/view/4371/4249)  
63. Parametrizations Tutorial — PyTorch Tutorials 2.7.0+cu126 ..., 访问时间为 七月 30, 2025， [https://docs.pytorch.org/tutorials/intermediate/parametrizations.html](https://docs.pytorch.org/tutorials/intermediate/parametrizations.html)  
64. torch.Tensor — PyTorch 2.7 documentation, 访问时间为 七月 29, 2025， [https://pytorch.org/docs/stable/tensors.html](https://pytorch.org/docs/stable/tensors.html)  
65. SNAPSHOT ENSEMBLES: TRAIN 1, GET M FOR FREE \- OpenReview, 访问时间为 七月 29, 2025， [https://openreview.net/references/pdf?id=BJYwwY9ll](https://openreview.net/references/pdf?id=BJYwwY9ll)  
66. gaohuang/SnapshotEnsemble: Snapshot Ensembles in Torch (Snapshot Ensembles: Train 1, Get M for Free) \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/gaohuang/SnapshotEnsemble](https://github.com/gaohuang/SnapshotEnsemble)  
67. Snapshot Ensembles: Train 1, get M for free, 访问时间为 七月 29, 2025， [https://arxiv.org/pdf/1704.00109](https://arxiv.org/pdf/1704.00109)  
68. \[2408.02707\] SnapE \-- Training Snapshot Ensembles of Link Prediction Models \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/abs/2408.02707](https://arxiv.org/abs/2408.02707)  
69. CS229 Project Final Report \- Reproduce and Explore Variations of SNAPSHOT ENSEMBLES, 访问时间为 七月 29, 2025， [https://cs229.stanford.edu/proj2017/final-reports/5244110.pdf](https://cs229.stanford.edu/proj2017/final-reports/5244110.pdf)  
70. NeuSE: A Neural Snapshot Ensemble Method for Collaborative Filtering \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/pdf/2104.07269](https://arxiv.org/pdf/2104.07269)  
71. The Geometric Median on Riemannian Manifolds with Application to ..., 访问时间为 七月 29, 2025， [https://pmc.ncbi.nlm.nih.gov/articles/PMC2735114/](https://pmc.ncbi.nlm.nih.gov/articles/PMC2735114/)  
72. Beyond R-barycenters: an effective averaging method on Stiefel and Grassmann manifolds, 访问时间为 七月 29, 2025， [https://arxiv.org/html/2501.11555v1](https://arxiv.org/html/2501.11555v1)  
73. Estimating Outlier-Immunized Common Harmonic Waves for Brain Network Analyses on the Stiefel Manifold \- PubMed, 访问时间为 七月 29, 2025， [https://pubmed.ncbi.nlm.nih.gov/37028067/](https://pubmed.ncbi.nlm.nih.gov/37028067/)  
74. (PDF) A Riemannian covariance for manifold-valued data \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/publication/384769306\_A\_Riemannian\_covariance\_for\_manifold-valued\_data](https://www.researchgate.net/publication/384769306_A_Riemannian_covariance_for_manifold-valued_data)  
75. A Nonlinear Regression Technique for Manifold Valued Data With Applications to Medical Image Analysis \- CVF Open Access, 访问时间为 七月 29, 2025， [https://openaccess.thecvf.com/content\_cvpr\_2016/papers/Banerjee\_A\_Nonlinear\_Regression\_CVPR\_2016\_paper.pdf](https://openaccess.thecvf.com/content_cvpr_2016/papers/Banerjee_A_Nonlinear_Regression_CVPR_2016_paper.pdf)  
76. Estimating Common Harmonic Waves of Brain Networks on Stiefel Manifold \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/publication/345260934\_Estimating\_Common\_Harmonic\_Waves\_of\_Brain\_Networks\_on\_Stiefel\_Manifold](https://www.researchgate.net/publication/345260934_Estimating_Common_Harmonic_Waves_of_Brain_Networks_on_Stiefel_Manifold)  
77. ManifoldNet: A Deep Neural Network for Manifold-Valued Data With Applications, 访问时间为 七月 29, 2025， [https://par.nsf.gov/servlets/purl/10472466](https://par.nsf.gov/servlets/purl/10472466)  
78. Model Averaging for Manifold Learning | OpenReview, 访问时间为 七月 29, 2025， [https://openreview.net/forum?id=U8PMpygECy](https://openreview.net/forum?id=U8PMpygECy)  
79. Geometric means of matrices beyond the positive definite cone \- MathOverflow, 访问时间为 七月 29, 2025， [https://mathoverflow.net/questions/139401/geometric-means-of-matrices-beyond-the-positive-definite-cone](https://mathoverflow.net/questions/139401/geometric-means-of-matrices-beyond-the-positive-definite-cone)  
80. Statistics on the Stiefel manifold: Theory and applications \- Project Euclid, 访问时间为 七月 29, 2025， [https://projecteuclid.org/journals/annals-of-statistics/volume-47/issue-1/Statistics-on-the-Stiefel-manifold-Theory-and-applications/10.1214/18-AOS1692.full](https://projecteuclid.org/journals/annals-of-statistics/volume-47/issue-1/Statistics-on-the-Stiefel-manifold-Theory-and-applications/10.1214/18-AOS1692.full)  
81. \[1312.7710\] Total variation regularization for manifold-valued data \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/abs/1312.7710](https://arxiv.org/abs/1312.7710)  
82. 1.11. Ensembles: Gradient boosting, random forests, bagging, voting, stacking \- Scikit-learn, 访问时间为 七月 29, 2025， [https://scikit-learn.org/stable/modules/ensemble.html](https://scikit-learn.org/stable/modules/ensemble.html)  
83. Data-free knowledge distillation in neural networks for regression \- ResearchGate, 访问时间为 七月 30, 2025， [https://www.researchgate.net/publication/349878093\_Data-free\_knowledge\_distillation\_in\_neural\_networks\_for\_regression](https://www.researchgate.net/publication/349878093_Data-free_knowledge_distillation_in_neural_networks_for_regression)  
84. en.wikipedia.org, 访问时间为 七月 30, 2025， [https://en.wikipedia.org/wiki/Knowledge\_distillation\#:\~:text=In%20machine%20learning%2C%20knowledge%20distillation,might%20not%20be%20fully%20utilized.](https://en.wikipedia.org/wiki/Knowledge_distillation#:~:text=In%20machine%20learning%2C%20knowledge%20distillation,might%20not%20be%20fully%20utilized.)  
85. \[2204.00548\] Unified and Effective Ensemble Knowledge Distillation \- arXiv, 访问时间为 七月 30, 2025， [https://arxiv.org/abs/2204.00548](https://arxiv.org/abs/2204.00548)  
86. Knowledge distillation \- Wikipedia, 访问时间为 七月 30, 2025， [https://en.wikipedia.org/wiki/Knowledge\_distillation](https://en.wikipedia.org/wiki/Knowledge_distillation)  
87. What is Knowledge distillation? | IBM, 访问时间为 七月 30, 2025， [https://www.ibm.com/think/topics/knowledge-distillation](https://www.ibm.com/think/topics/knowledge-distillation)  
88. \[2012.09816\] Towards Understanding Ensemble, Knowledge Distillation and Self-Distillation in Deep Learning \- arXiv, 访问时间为 七月 30, 2025， [https://arxiv.org/abs/2012.09816](https://arxiv.org/abs/2012.09816)  
89. Distilling Knowledge for Search-based Structured Prediction \- ACL Anthology, 访问时间为 七月 30, 2025， [https://aclanthology.org/P18-1129/](https://aclanthology.org/P18-1129/)  
90. Knowledge Distillation for Regression \- MATLAB Answers, 访问时间为 七月 30, 2025， [https://www.mathworks.com/matlabcentral/answers/2045830-knowledge-distillation-for-regression](https://www.mathworks.com/matlabcentral/answers/2045830-knowledge-distillation-for-regression)  
91. Uncertainty-aware genomic deep learning with knowledge distillation \- PMC \- PubMed Central, 访问时间为 七月 30, 2025， [https://pmc.ncbi.nlm.nih.gov/articles/PMC11601481/](https://pmc.ncbi.nlm.nih.gov/articles/PMC11601481/)  
92. Structured Knowledge Distillation for Dense Prediction \- arXiv, 访问时间为 七月 30, 2025， [https://arxiv.org/pdf/1903.04197](https://arxiv.org/pdf/1903.04197)  
93. Everything You Need to Know about Knowledge Distillation \- Hugging Face, 访问时间为 七月 30, 2025， [https://huggingface.co/blog/Kseniase/kd](https://huggingface.co/blog/Kseniase/kd)  
94. Categories of Response-Based, Feature-Based, and Relation-Based Knowledge Distillation | Request PDF \- ResearchGate, 访问时间为 七月 30, 2025， [https://www.researchgate.net/publication/371548957\_Categories\_of\_Response-Based\_Feature-Based\_and\_Relation-Based\_Knowledge\_Distillation](https://www.researchgate.net/publication/371548957_Categories_of_Response-Based_Feature-Based_and_Relation-Based_Knowledge_Distillation)  
95. What is Knowledge Distillation? A Deep Dive. \- Roboflow Blog, 访问时间为 七月 30, 2025， [https://blog.roboflow.com/what-is-knowledge-distillation/](https://blog.roboflow.com/what-is-knowledge-distillation/)  
96. Understanding Knowledge Distillation in Simple Steps | by Satya \- Medium, 访问时间为 七月 30, 2025， [https://medium.com/@satya15july\_11937/understanding-knowledge-distillation-in-simple-steps-3310ef01f5e0](https://medium.com/@satya15july_11937/understanding-knowledge-distillation-in-simple-steps-3310ef01f5e0)  
97. Understanding Knowledge Distillation: In Simple Terms | by Nikita Parate | Medium, 访问时间为 七月 30, 2025， [https://medium.com/@nikitaparate9/understanding-knowledge-distillation-in-simple-terms-01c247ee8a72](https://medium.com/@nikitaparate9/understanding-knowledge-distillation-in-simple-terms-01c247ee8a72)  
98. Test Time Augmentation (Experimental) \- PyTorch Tabular, 访问时间为 七月 29, 2025， [https://pytorch-tabular.readthedocs.io/en/latest/tutorials/11-Test%20Time%20Augmentation/](https://pytorch-tabular.readthedocs.io/en/latest/tutorials/11-Test%20Time%20Augmentation/)  
99. Understanding Test-Time Augmentation \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/html/2402.06892v1](https://arxiv.org/html/2402.06892v1)  
100. (PDF) Test-time augmentation for deep learning-based cell segmentation on microscopy images \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/publication/340031950\_Test-time\_augmentation\_for\_deep\_learning-based\_cell\_segmentation\_on\_microscopy\_images](https://www.researchgate.net/publication/340031950_Test-time_augmentation_for_deep_learning-based_cell_segmentation_on_microscopy_images)  
101. Test-Time Augmentation for Document Image Binarization \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/publication/371840068\_Test-Time\_Augmentation\_for\_Document\_Image\_Binarization](https://www.researchgate.net/publication/371840068_Test-Time_Augmentation_for_Document_Image_Binarization)  
102. (PDF) Understanding Test-Time Augmentation \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/publication/356792655\_Understanding\_Test-Time\_Augmentation](https://www.researchgate.net/publication/356792655_Understanding_Test-Time_Augmentation)  
103. Test-Time Augmentation For Tabular Data With Scikit-Learn \- MachineLearningMastery.com, 访问时间为 七月 29, 2025， [https://machinelearningmastery.com/test-time-augmentation-with-scikit-learn/](https://machinelearningmastery.com/test-time-augmentation-with-scikit-learn/)  
104. Feature Augmentation Based Test-Time Adaptation \- CVF Open Access, 访问时间为 七月 29, 2025， [https://openaccess.thecvf.com/content/WACV2025/papers/Cho\_Feature\_Augmentation\_Based\_Test-Time\_Adaptation\_WACV\_2025\_paper.pdf](https://openaccess.thecvf.com/content/WACV2025/papers/Cho_Feature_Augmentation_Based_Test-Time_Adaptation_WACV_2025_paper.pdf)  
105. When and Why Test-Time Augmentation Works \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/publication/346143761\_When\_and\_Why\_Test-Time\_Augmentation\_Works](https://www.researchgate.net/publication/346143761_When_and_Why_Test-Time_Augmentation_Works)  
106. Test-Time Augmentation Meets Variational Bayes \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/html/2409.12587v1](https://arxiv.org/html/2409.12587v1)