

# **面向无线鲁棒SVD算子的最优可行性方案重构**

## **1\. AI使能无线鲁棒SVD算子挑战的原则性框架重塑**

为响应对先前方案可行性的关切，并追求竞赛背景下的最优解，本报告对“AI使能的无线鲁棒SVD算子”这一挑战进行了根本性的重新审视与框架重构。此方案摒弃了可能存在的脆弱假设与间接方法，建立在一个由几何约束、双目标优化和先进深度学习策略构成的坚实理论基础之上，旨在从根本上提升方案的可行性、鲁棒性与最终性能。

### **1.1 解构双目标优化问题：近似误差(AE)与计算复杂度**

本次竞赛的核心并非单一的精度竞赛，而是一个复杂且务实的双目标优化问题。评价体系明确规定了两个核心指标：近似误差（Approximation Error, AE）和模型前向传播的乘加运算次数（Multiply-Accumulate operations, MACs），即计算复杂度 1。最终排名规则为：首先依据AE进行排序，然后在划分的性能档位内，根据计算复杂度进行二次排序 1。

这一规则揭示了一个关键的寻优方向：解决方案必须在模型的预测精度与计算效率之间寻求帕累托最优（Pareto Optimality）。一个拥有极低AE但计算量庞大的模型，或一个极其轻量但精度不足的模型，都无法在这一评价体系中胜出。因此，所有架构设计决策都必须服务于“在给定的计算资源预算内，最大化地降低AE”这一核心原则。分档排序机制进一步暗示，AE的显著降低比复杂度的边际减少更具价值，但如果一个高复杂度模型的AE仅略优于一个轻量级竞争者，它将在同档位竞争中处于劣势。

这种对效率和性能双重要求的内在约束，引导我们将目光从单纯追求模型容量的重量级架构（如标准的Vision Transformer 2），转向那些在“单位算力性能”上表现更优的现代高效网络。例如，ConvNeXt架构的设计初衷便是在保持卷积网络（CNN）高效率的同时，融入Transformer的设计思想以达到卓越性能，这使其成为解决此类双目标优化问题的理想候选者 3。

### **1.2 以近似误差(AE)评价准则为核心导向**

竞赛的AE评价准则为模型的设计与优化提供了最直接的指导。该指标由三个部分构成，通过弗罗贝尼乌斯范数（Frobenius norm）进行量化 1：

AEi​=∥Hlabeli,:,:​​∥F​∥Hlabeli,:,:​​−Ui,:,:​Σi,:,:​Vi,:,:∗​∥F​​+∥Ui,:,:∗​Ui,:,:​−I∥F​+∥Vi,:,:∗​Vi,:,:​−I∥F​  
此公式明确包含了三个考核点：

1. **归一化重构误差**：衡量模型输出的SVD分量（U,Σ,V）重构理想信道矩阵$H\_{\\text{label}}$的精确度。  
2. **左奇异矩阵正交性误差**：惩罚输出的U矩阵偏离酉矩阵（Unitary Matrix）的程度。  
3. **右奇异矩阵正交性误差**：惩罚输出的V矩阵偏离酉矩阵的程度。

将正交性约束直接纳入最终评价指标，是一个至关重要的信号。它表明，任何仅仅依赖低重构误差而忽视矩阵结构约束的方案都将受到严厉惩罚，从而被认定为不可行。为了使优化过程与评价标准完全对齐，模型的训练损失函数必须直接映射AE的这种结构。一个仅依赖重构损失，寄希望于网络能“隐式”学习到正交性的方案，其基础是脆弱且不可靠的。

从更深层次看，AE的结构天然地将此问题定义为一个多任务学习（Multi-task Learning）挑战。神经网络需要同时完成三个子任务：精确重构信道、生成酉矩阵U以及生成酉矩阵V。这一视角启发我们，模型架构应包含一个用于提取共享特征的“主干网络”，以及三个分别用于预测U、s（奇异值向量）和V的独立“预测头”。总损失函数相应地设计为各任务损失的加权和，这不仅与AE指标完美对应，也为引入辅助损失（Auxiliary Loss）等技术来稳定和加速训练提供了理论依据 4。

### **1.3 几何视角：构建面向斯蒂费尔流形(Stiefel Manifold)的映射学习任务**

SVD的输出矩阵$U \\in \\mathbb{C}^{M \\times R}$和$V \\in \\mathbb{C}^{N \\times R}$必须是酉矩阵，即它们的列向量是标准正交的。所有这类$m \\times r$复数矩阵构成的集合在数学上被称为复数斯蒂费尔流形（Complex Stiefel Manifold），记作St(r,m) 5。这是一个具有特定几何结构的非欧几里得空间。

然而，标准的神经网络层（如nn.Linear或nn.Conv2d）的输出本质上是欧几里得空间中的无约束向量。直接使用这些层来预测U和V的元素，其输出几乎不可能自动满足严格的标准正交约束。因此，本任务的核心技术挑战从根本上是一个几何问题：如何设计一个网络层，使其能够将从欧几里得参数空间中学到的信息，确定性地映射到目标流形——斯蒂费尔流形之上。

将问题置于此几何框架下，为我们开启了一扇通往更优越、更可行解决方案的大门，这些方案源自几何深度学习与黎曼优化领域 5。与其采用基于惩罚项的损失函数（一种间接且往往不稳定的方法）来“鼓励”网络输出“靠近”流形，我们可以设计一个终端层，通过其内在的数学构造来“保证”其输出始终位于流形之上。这是一种更为稳健和原则性的方法，直接回应了对方案可行性的担忧。凯莱变换（Cayley Transform）便是实现这种映射的强大工具之一，它能在可微分的框架内，将一个斜埃尔米特矩阵（Skew-Hermitian Matrix）映射为一个酉矩阵 9。这一洞察构成了本重构方案的理论基石，将一个不稳定的优化问题转化为一个稳定的架构设计问题，从而极大地提升了方案的可行性。

## **2\. 方案架构：轻量化、几何感知的SVDNet**

基于上述原则性框架，我们设计了一个名为SVDNet的全新神经网络架构。该架构的每一个组件都经过精心选择与论证，旨在同时满足AE最小化、MACs可控以及几何约束三大核心要求。

### **2.1 骨干网络选择：轻量化ConvNeXt-UNet编码器**

输入数据为Nsamp​×M×N×2的四维张量，其中M和N通常为64 1。我们可以将其视为

$N\_{\\text{samp}}$个尺寸为$M \\times N$、包含实部和虚部两个通道的“图像”。任务要求模型具备高计算效率，因此骨干网络的选择至关重要。

* **Transformers (如Swin-Transformer)**: 尽管在捕捉长距离依赖方面表现出色，且有研究表明其能学习线性代数运算，但对于本任务的输入尺寸而言，其计算成本可能过高，且实现高精度数值计算通常需要庞大的模型 12。  
* **传统CNNs (如ResNet)**: 作为成熟的特征提取器，性能可靠，但现代架构在性能与效率的平衡上已超越它们 16。  
* **ConvNeXt**: 该架构通过引入Transformer的设计精髓（如倒置瓶颈结构、大卷积核、层归一化）来“现代化”传统CNN，同时保留了卷积的高效性 3。它广泛使用计算成本更低的深度可分离卷积（Depthwise Separable Convolutions）18，是实现高精度与低MACs目标的理想选择。

我们提出的方案采用**U-Net架构** 20，并以一个

**轻量化的ConvNeXt模型（如ConvNeXt-T）作为其编码器主干**。U-Net的编码器-解码器结构及跳跃连接（Skip Connections）非常适合于需要保留精细空间信息的任务，这与保留信道矩阵结构特征的需求高度契合 22。

### **2.2 核心创新：可微分缩放凯莱变换层(Scaled Cayley Transform Layer)**

为解决将网络输出映射到斯蒂费尔流形的几何挑战，我们引入了可微分的缩放凯莱变换层作为核心创新。

标准的凯莱变换将一个斜对称/斜埃尔米特矩阵A映射到一个正交/酉矩阵Q：Q=(I−A)(I+A)−1 9。然而，标准变换无法表示所有特征值中包含-1的酉矩阵。

**缩放凯莱变换**通过引入一个对角缩放矩阵D（对角元素为$\\pm 1$）解决了这个问题，能够表示任意酉矩阵：Q=(I−A)(I+A)−1D 26。

我们将此变换实现为一个自定义的torch.nn.Module，作为预测U和V的两个网络分支的最终输出层。其工作流程如下：

1. 网络前端（线性层）输出一个无约束的参数向量。  
2. 该向量在层内部被重塑为一个斜埃尔米特矩阵A（AH=−A）。对于一个k×k的复数矩阵，这需要$k(k-1)$个实数参数来定义其上三角部分的实部和虚部，下三角部分由共轭转置性质确定 28。  
3. 该层随后执行变换Q=(I−A)D(I+A)−1。在PyTorch中，复数矩阵的加法、乘法和求逆(torch.linalg.inv)均为可微分操作，确保了梯度能够顺畅地反向传播。

通过这种方式，我们以构造性的方法保证了U和V的输出在数值精度允许的范围内严格满足酉矩阵的定义，从而根本性地解决了正交性约束问题。

### **2.3 SVDNet架构蓝图**

SVDNet的整体架构设计如下：

* **输入层**: 将Nsamp​×M×N×2的输入重塑为$(N\_{\\text{samp}}, 2, M, N)$，以便作为双通道图像输入到2D卷积网络中。  
* **编码器 (ConvNeXt Backbone)**: 采用轻量化的ConvNeXt-T作为特征提取器。它包含多个阶段，通过下采样层（步长为2的卷积）和ConvNeXt模块，逐步减小特征图的空间尺寸，同时增加通道深度，以学习从局部到全局的层次化特征。  
* **瓶颈层**: 在最低分辨率处设置一个ConvNeXt模块，用于处理最高度抽象的特征。  
* **解码器与三预测头**: 解码器通过上采样操作（如转置卷积或双线性插值+卷积）逐步恢复特征图的分辨率。在每一级上采样后，通过跳跃连接融合来自编码器对应层级的特征，以补充高频细节信息。最终，在恢复到原始分辨率的特征图后，接入三个独立的预测头：  
  1. **奇异值头 (s-Head)**: 由一个卷积层和一个线性层构成，直接回归预测R个奇异值。使用ReLU激活函数确保其输出非负。  
  2. **左奇异矩阵头 (U-Head)**: 由一个卷积层和一个线性层构成，输出一个长度为$M(M-1)$的向量。该向量被送入\*\*缩放凯莱变换层\*\*，生成最终的$M \\times R$酉矩阵U。  
  3. **右奇异矩阵头 (V-Head)**: 结构与U-Head相同，但输出一个长度为$N(N-1)$的向量，并由其专属的\*\*缩放凯莱变换层\*\*生成$N \\times R$的酉矩阵V。

为了更清晰地论证骨干网络的选择，下表对比了几个候选架构。

**表 1: 骨干网络候选架构对比分析**

| 架构 | 关键特性 | 预估MACs (64x64输入) | 优点 (针对本任务) | 缺点 (针对本任务) |
| :---- | :---- | :---- | :---- | :---- |
| ResNet50-UNet | 成熟的残差连接结构 | 中等 | 稳定可靠，预训练模型丰富 | 相比现代架构，效率较低 |
| Swin-T-UNet | 基于窗口移动的自注意力机制 | 较高 | 强大的全局特征建模能力 | 计算复杂度高，可能对本任务过拟合 |
| **ConvNeXt-T-UNet (本方案)** | 现代化的CNN，深度可分离卷积，倒置瓶颈 | **较低** | **在效率和性能间达到最佳平衡，专为高效率设计** | 相对较新，生态系统不如ResNet成熟 |

分析显示，ConvNeXt-T-UNet在满足高性能需求的同时，提供了最低的计算复杂度，完美契合了竞赛的双目标评价准则，是当前任务的最优选择。

## **3\. 先进训练与推理策略：实现卓越鲁棒性与精度**

一个优越的架构需要配合先进的训练与推理策略，才能充分发挥其潜力。本节将详细阐述为SVDNet量身定制的策略，旨在最大化模型在非理想信道数据上的鲁棒性与精度 1。

### **3.1 精准对齐AE指标的多分量损失函数**

如前所述，训练目标必须与评价指标严格对齐。因此，我们设计了一个由三部分加权构成的总损失函数Ltotal​：

Ltotal​=λrec​Lrec​+λorthoU​​LorthoU​​+λorthoV​​LorthoV​​

* **重构损失 (Lrec​)**: 这是模型的核心任务，定义为重构信道矩阵与理想信道标签之间的弗罗贝尼乌斯范数：Lrec​=∥Hlabel​−U⋅diag(s)⋅V∗∥F​。  
* **辅助正交性损失 (LorthoU​​,LorthoV​​)**: 尽管凯莱变换层在理论上保证了输出的酉性，但在float32有限精度计算中可能存在微小偏差。引入这两个辅助损失项作为正则化器，可以促使网络学习到更稳定的参数，从而在实践中更严格地强制酉性约束。它们被定义为：LorthoU​​=∥U∗U−I∥F​ 和 LorthoV​​=∥V∗V−I∥F​。损失权重$\\lambda$将作为超参数进行细致调整。

这种设计体现了对实际工程挑战的深刻理解：它不仅实现了理论上的优雅，还通过辅助损失主动规避了有限精度计算可能带来的潜在问题，进一步增强了方案的稳健性和可行性。

### **3.2 快照集成与余弦退火：以单一训练成本构建多样化模型集成**

竞赛要求一个模型需在三个不同的场景中都表现良好，这暗示了模型必须具备强大的泛化能力 1。传统的单一模型训练可能会收敛到某个特定的局部最优解，从而对某些未见过的信道统计特性表现不佳。为了解决这个问题，我们采用\*\*快照集成（Snapshot Ensembling）\*\*技术 30。

该技术的核心是使用**循环余弦退火（Cyclic Cosine Annealing）学习率调度策略** 30，而非传统的单调递减学习率。具体实施如下：

1. 将总训练周期（例如300个epochs）划分为M个循环（例如5个循环，每个60个epochs）。  
2. 在每个循环开始时，将学习率重置为一个较高的初始值。  
3. 在循环内部，学习率遵循余弦函数曲线平滑地退火至接近零。  
4. 在每个循环结束，学习率最低点时，模型收敛到一个局部最优解。此时，我们保存当前模型的权重，作为一个“快照”。  
5. 随后，学习率被再次重置为高初始值，强大的梯度更新将模型“踢出”当前的局部最优，使其在下一个循环中探索损失函数的其他区域。

通过一次完整的训练过程，我们能够获得M个位于不同局部最优解的、具有多样性的模型。每个快照模型可能对不同类型的信道数据有各自的偏好和专长。通过将它们集成起来，最终的预测结果将更加鲁棒，泛化能力更强，从而能更好地应对多个未知场景的挑战。

### **3.3 面向流形值数据的测试时增强(TTA)：构建鲁棒的推理策略**

为了在推理阶段进一步提升模型的性能和鲁棒性，我们引入了\*\*测试时增强（Test-Time Augmentation, TTA）\*\*策略 36。然而，由于输出

U和V是流形上的数据，必须采用几何上正确的方法来聚合预测结果。

我们的推理流程如下，对每个测试样本$H\_{\\text{noisy}}$和$M$个快照模型中的每一个都执行：

1. **生成增强输入**: 对$H\_{\\text{noisy}}$进行$K$次数据增强，生成一个增强集合。这些增强方法应具有物理意义，例如添加微小的随机高斯噪声、取共轭转置等。  
2. **对增强输入进行预测**: 将K个增强后的信道矩阵输入模型，得到K组$(U, s, V)预测结果。对于共轭转置等操作，需要在输出端进行相应的逆变换（例如，交换并共轭U和V$）。  
3. **聚合预测结果（关键步骤）**:  
   * **奇异值 s**: 它们是欧几里得空间中的实数，可以直接通过计算K个预测向量的**算术平均值**来聚合。  
   * **奇异矩阵 U,V**: 它们位于斯蒂费尔流形上，直接求算术平均会破坏其酉性。因此，必须采用流形上的平均算法。我们选用**几何中位数（Geometric Median）**，它通过最小化到所有数据点的测地线距离之和来定义中心点，相比于弗雷歇均值（Fréchet mean），它对异常值更具鲁棒性 42。我们将通过迭代的Weiszfeld算法来计算  
     K个预测U矩阵和V矩阵的几何中位数。  
4. **最终集成预测**: 对M个快照模型重复上述1-3步，得到M个经过TTA聚合后的$(U\_{\\text{TTA}}, s\_{\\text{TTA}}, V\_{\\text{TTA}})。最终的输出结果是这M组结果的再次聚合：对s\_{\\text{TTA}}求算术平均，对U\_{\\text{TTA}}和V\_{\\text{TTA}}$求几何中位数。

下表详细说明了这一复杂的、但几何上完备的推理策略。

**表 2: 测试时增强(TTA)与集成平均策略**

| 输出分量 | 数据空间 | 输入端增强 | 输出端逆变换 | 聚合方法 | 理由 |
| :---- | :---- | :---- | :---- | :---- | :---- |
| s (奇异值) | 欧几里得空间 (RR) | 高斯噪声, 共轭转置 | 无 | 算术平均 | 欧氏空间标准平均，计算简单高效 |
| U (左奇异矩阵) | 斯蒂费尔流形 (St(R,M)) | 高斯噪声, 共轭转置 | 交换并共轭U,V | **几何中位数** | 保持输出的酉性结构，对异常预测值鲁棒 |
| V (右奇异矩阵) | 斯蒂费尔流形 (St(R,N)) | 高斯噪声, 共轭转置 | 交换并共轭U,V | **几何中位数** | 保持输出的酉性结构，对异常预测值鲁棒 |

该策略确保了从模型架构到最终预测的每一个环节都充分尊重了问题的内在几何结构，是实现最高精度和鲁棒性的关键保障。

## **4\. 预期性能与可行性分析**

本节将正面回应关于可行性的关切，通过具体的计算分析、性能预期和实施规划，论证本方案的高度可行性与优越性。

### **4.1 计算复杂度分解与MACs估算**

SVDNet的计算复杂度主要由ConvNeXt编码器贡献。得益于深度可分离卷积的应用，ConvNeXt-T的MACs远低于同等性能的ResNet或Swin Transformer。凯莱变换层虽然包含矩阵求逆操作，但其操作的矩阵尺寸（例如64×64）相对较小，其计算量与庞大的卷积主干网络相比可以忽略不计。经过初步估算，整个SVDNet模型的MACs将处于一个极具竞争力的轻量级范围内，完全符合竞赛对模型效率的要求。

### **4.2 在AE-复杂度权衡曲线上的预期定位**

我们坚信，本方案的设计将在AE-复杂度二维评估平面上占据领先位置。

* **低复杂度**: 通过轻量化的ConvNeXt-UNet骨干网络实现。  
* **低AE**: 通过以下四个方面的协同作用达成：  
  1. **几何感知的架构**: 凯莱变换层从根本上消除了由非结构化输出导致的正交性误差。  
  2. **目标对齐的损失函数**: 确保优化过程的每一步都直接服务于降低最终的AE指标。  
  3. **最大化泛化能力的训练策略**: 快照集成技术有效提升了模型跨场景的泛化能力。  
  4. **最大化鲁棒性的推理策略**: 基于流形几何的TTA策略能够有效抑制噪声和输入扰动对最终结果的影响。

本方案是一个系统性的工程，其每个组件都旨在解决一个可能导致性能下降或可行性问题的具体挑战。这种各部分协同增效的整体设计，使其从“一系列好想法的堆砌”升华为一个真正意义上的“最优解决方案”。

### **4.3 实施路线图与风险规避**

为确保方案的顺利实施，我们制定了清晰的路线图和风险应对策略。

**实施路线图**:

1. **阶段一 (核心组件开发)**: 实现并单元测试复数域的缩放凯莱变换nn.Module。搭建包含ConvNeXt-UNet主干和三预测头的SVDNet架构。  
2. **阶段二 (训练流程搭建)**: 开发包含多分量损失函数、循环余弦退火学习率调度器和模型快照保存功能的训练脚本。  
3. **阶段三 (推理与评估流程)**: 开发实现TTA流程的推理脚本，其中包含高效的几何中位数计算模块。  
4. **阶段四 (实验与调优)**: 在官方数据集上进行模型训练，精调超参数（如损失权重、学习率、快照数量等），并在验证集上评估性能。

**风险与规避措施**:

* **风险**: 凯莱变换中的矩阵求逆可能出现数值不稳定。  
  * **规避**: 若有必要，在求逆步骤中使用torch.float64双精度计算以提高稳定性 44；同时，辅助正交性损失将作为正则项，引导网络学习到使  
    I+A矩阵良态的参数。  
* **风险**: 几何中位数计算耗时较长。  
  * **规避**: 实现高效的、向量化的Weiszfeld迭代算法 42。由于该计算仅在推理阶段对少量矩阵（  
    K个TTA样本和M个快照模型）执行，对总测试时间影响极小。  
* **风险**: 单一模型在多场景下泛化失败。  
  * **规避**: 这正是快照集成与TTA策略所要解决的核心风险。通过模型多样性的集成，可以极大地缓解此问题。

## **5\. 结论：通往最优解的路径**

### **5.1 框架优势综合**

本报告提出的重构方案，其核心优势在于系统性地解决了“AI使能的无线鲁棒SVD算子”这一挑战的内在复杂性。它通过以下三个层面构建了一个全面且强大的解决方案：

* **几何正确性**: 引入可微分的缩放凯莱变换层，从架构层面保证了输出奇异矩阵的酉性，将一个困难的约束优化问题转化为一个更易于处理的无约束优化问题。  
* **计算高效性**: 选用轻量化的ConvNeXt-UNet作为骨干网络，在保证强大特征提取能力的同时，严格控制了计算复杂度，完美契合了竞赛的双目标评价准则。  
* **极致鲁棒性**: 结合快照集成训练策略和面向流形数据的测试时增强推理策略，最大限度地提升了模型的泛化能力和对信道非理想因素的鲁棒性。

### **5.2 为何此方案代表“最优解”**

此方案不仅是对先前方案的改进，更是一次基于第一性原理的重构，使其成为当前挑战下的“最优解”。其优越性体现在：它摒弃了可能导致可行性问题的、基于惩罚的启发式方法，转而采用结构上完备的几何方法来处理核心约束；它没有盲目追求模型规模，而是通过精心选择高效架构来应对实际的算力限制；它通过融合业界前沿的训练和推理技术，全面地解决了泛化和鲁棒性这一关键难题。

综上所述，本方案在理论的严谨性、架构的先进性、策略的有效性和实施的可行性上均达到了高度统一。它为在本次竞赛中取得优异成绩提供了一条清晰、可靠且最具潜力的技术路径。

#### **引用的著作**

1. QA.docx  
2. arXiv:2010.11929v2 \[cs.CV\] 3 Jun 2021, 访问时间为 七月 29, 2025， [https://arxiv.org/pdf/2010.11929](https://arxiv.org/pdf/2010.11929)  
3. A ConvNet for the 2020s \- CVF Open Access, 访问时间为 七月 29, 2025， [https://openaccess.thecvf.com/content/CVPR2022/papers/Liu\_A\_ConvNet\_for\_the\_2020s\_CVPR\_2022\_paper.pdf](https://openaccess.thecvf.com/content/CVPR2022/papers/Liu_A_ConvNet_for_the_2020s_CVPR_2022_paper.pdf)  
4. Auxiliary Loss | Envisioning Vocab, 访问时间为 七月 29, 2025， [https://www.envisioning.io/vocab/auxiliary-loss](https://www.envisioning.io/vocab/auxiliary-loss)  
5. Coordinate descent on the Stiefel manifold for deep neural network training, 访问时间为 七月 29, 2025， [https://www.esann.org/sites/default/files/proceedings/2023/ES2023-143.pdf](https://www.esann.org/sites/default/files/proceedings/2023/ES2023-143.pdf)  
6. Beyond R-barycenters: an effective averaging method on Stiefel and Grassmann manifolds, 访问时间为 七月 29, 2025， [https://arxiv.org/html/2501.11555v1](https://arxiv.org/html/2501.11555v1)  
7. Solving Optimization Problems over the Stiefel Manifold by Smooth Exact Penalty Functions, 访问时间为 七月 29, 2025， [https://optimization-online.org/wp-content/uploads/2021/10/ExPenv2.1.pdf](https://optimization-online.org/wp-content/uploads/2021/10/ExPenv2.1.pdf)  
8. Retraction-free optimization over the Stiefel manifold with application to the LoRA fine-tuning, 访问时间为 七月 29, 2025， [https://openreview.net/forum?id=GP30inajOt\&referrer=%5Bthe%20profile%20of%20Jiang%20Hu%5D(%2Fprofile%3Fid%3D\~Jiang\_Hu2)](https://openreview.net/forum?id=GP30inajOt&referrer=%5Bthe+profile+of+Jiang+Hu%5D\(/profile?id%3D~Jiang_Hu2\))  
9. Supplementary Information, 访问时间为 七月 29, 2025， [https://proceedings.neurips.cc/paper\_files/paper/2023/file/6ac807c9b296964409b277369e55621a-Supplemental-Conference.pdf](https://proceedings.neurips.cc/paper_files/paper/2023/file/6ac807c9b296964409b277369e55621a-Supplemental-Conference.pdf)  
10. Orthogonalizing Convolutional Layers with the Cayley Transform \- OpenReview, 访问时间为 七月 29, 2025， [https://openreview.net/pdf?id=Pbj8H\_jEHYv](https://openreview.net/pdf?id=Pbj8H_jEHYv)  
11. Cheap Orthogonal Constraints in Neural Networks: A Simple Parametrization of the Orthogonal and Unitary Group \- Proceedings of Machine Learning Research, 访问时间为 七月 29, 2025， [http://proceedings.mlr.press/v97/lezcano-casado19a/lezcano-casado19a.pdf](http://proceedings.mlr.press/v97/lezcano-casado19a/lezcano-casado19a.pdf)  
12. Linear algebra with transformers, 访问时间为 七月 29, 2025， [https://arxiv.org/pdf/2112.01898](https://arxiv.org/pdf/2112.01898)  
13. Linear algebra with transformers | Request PDF \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/publication/356800102\_Linear\_algebra\_with\_transformers](https://www.researchgate.net/publication/356800102_Linear_algebra_with_transformers)  
14. SwinIR: Image Restoration Using Swin Transformer \- CVF Open Access, 访问时间为 七月 29, 2025， [https://openaccess.thecvf.com/content/ICCV2021W/AIM/papers/Liang\_SwinIR\_Image\_Restoration\_Using\_Swin\_Transformer\_ICCVW\_2021\_paper.pdf](https://openaccess.thecvf.com/content/ICCV2021W/AIM/papers/Liang_SwinIR_Image_Restoration_Using_Swin_Transformer_ICCVW_2021_paper.pdf)  
15. skchen1993/SwinIR: experiment for reproducing SwinIR result \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/skchen1993/SwinIR](https://github.com/skchen1993/SwinIR)  
16. U-Nets with ResNet Encoders and cross connections | by Chris ..., 访问时间为 七月 29, 2025， [https://medium.com/data-science/u-nets-with-resnet-encoders-and-cross-connections-d8ba94125a2c](https://medium.com/data-science/u-nets-with-resnet-encoders-and-cross-connections-d8ba94125a2c)  
17. What is the use of BackBone in UNET model ? | Kaggle, 访问时间为 七月 29, 2025， [https://www.kaggle.com/general/205141](https://www.kaggle.com/general/205141)  
18. Depthwise-Separable convolutions in Pytorch | by Diego Velez | FAUN.dev, 访问时间为 七月 29, 2025， [https://faun.pub/depthwise-separable-convolutions-in-pytorch-fd41a97327d0](https://faun.pub/depthwise-separable-convolutions-in-pytorch-fd41a97327d0)  
19. Falcon: lightweight and accurate convolution based on depthwise separable convolution, 访问时间为 七月 29, 2025， [https://snu.elsevierpure.com/en/publications/falcon-lightweight-and-accurate-convolution-based-on-depthwise-se](https://snu.elsevierpure.com/en/publications/falcon-lightweight-and-accurate-convolution-based-on-depthwise-se)  
20. (a) Architecture of U-Net with ResNet-50 backbone. (b) Diagram of... \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/figure/a-Architecture-of-U-Net-with-ResNet-50-backbone-b-Diagram-of-bottleneck-residual\_fig4\_361144570](https://www.researchgate.net/figure/a-Architecture-of-U-Net-with-ResNet-50-backbone-b-Diagram-of-bottleneck-residual_fig4_361144570)  
21. arpsn123/Multiclass\_Segmentation\_using\_by\_UNET\_with\_RESNET\_as\_Backbone: Employing a fusion of UNet and ResNet architectures, the project endeavors to achieve multiclass semantic segmentation of sandstone images. Through deep learning techniques, it seeks to uncover microstructural features across various geological classifications. \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/arpsn123/Multiclass\_Segmentation\_using\_by\_UNET\_with\_RESNET\_as\_Backbone](https://github.com/arpsn123/Multiclass_Segmentation_using_by_UNET_with_RESNET_as_Backbone)  
22. Lightweight Deep Learning Model, ConvNeXt-U: An Improved U-Net Network for Extracting Cropland in Complex Landscapes from Gaofen-2 Images \- PubMed, 访问时间为 七月 29, 2025， [https://pubmed.ncbi.nlm.nih.gov/39797051/](https://pubmed.ncbi.nlm.nih.gov/39797051/)  
23. Lightweight Deep Learning Model, ConvNeXt-U: An Improved U-Net Network for Extracting Cropland in Complex Landscapes from Gaofen-2 Images \- MDPI, 访问时间为 七月 29, 2025， [https://www.mdpi.com/1424-8220/25/1/261](https://www.mdpi.com/1424-8220/25/1/261)  
24. CI-UNet: melding convnext and cross-dimensional attention for robust medical image segmentation \- PMC, 访问时间为 七月 29, 2025， [https://pmc.ncbi.nlm.nih.gov/articles/PMC10874369/](https://pmc.ncbi.nlm.nih.gov/articles/PMC10874369/)  
25. Cayley transform \- Wikipedia, 访问时间为 七月 29, 2025， [https://en.wikipedia.org/wiki/Cayley\_transform](https://en.wikipedia.org/wiki/Cayley_transform)  
26. Orthogonal Recurrent Neural Networks with Scaled Cayley Transform, 访问时间为 七月 29, 2025， [http://proceedings.mlr.press/v80/helfrich18a/helfrich18a.pdf](http://proceedings.mlr.press/v80/helfrich18a/helfrich18a.pdf)  
27. Orthogonal Recurrent Neural Networks with Scaled Cayley ... \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/pdf/1707.09520](https://arxiv.org/pdf/1707.09520)  
28. How to map a vector to a skew symmetric (or upper triangular) matrix? \- PyTorch Forums, 访问时间为 七月 29, 2025， [https://discuss.pytorch.org/t/how-to-map-a-vector-to-a-skew-symmetric-or-upper-triangular-matrix/40929](https://discuss.pytorch.org/t/how-to-map-a-vector-to-a-skew-symmetric-or-upper-triangular-matrix/40929)  
29. Skew symmetric matrix of vector \- Math Stack Exchange, 访问时间为 七月 29, 2025， [https://math.stackexchange.com/questions/2248413/skew-symmetric-matrix-of-vector](https://math.stackexchange.com/questions/2248413/skew-symmetric-matrix-of-vector)  
30. SNAPSHOT ENSEMBLES: TRAIN 1, GET M FOR FREE \- OpenReview, 访问时间为 七月 29, 2025， [https://openreview.net/references/pdf?id=BJYwwY9ll](https://openreview.net/references/pdf?id=BJYwwY9ll)  
31. gaohuang/SnapshotEnsemble: Snapshot Ensembles in Torch (Snapshot Ensembles: Train 1, Get M for Free) \- GitHub, 访问时间为 七月 29, 2025， [https://github.com/gaohuang/SnapshotEnsemble](https://github.com/gaohuang/SnapshotEnsemble)  
32. Snapshot Ensembles: Train 1, get M for free, 访问时间为 七月 29, 2025， [https://arxiv.org/pdf/1704.00109](https://arxiv.org/pdf/1704.00109)  
33. \[2408.02707\] SnapE \-- Training Snapshot Ensembles of Link Prediction Models \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/abs/2408.02707](https://arxiv.org/abs/2408.02707)  
34. CS229 Project Final Report \- Reproduce and Explore Variations of SNAPSHOT ENSEMBLES, 访问时间为 七月 29, 2025， [https://cs229.stanford.edu/proj2017/final-reports/5244110.pdf](https://cs229.stanford.edu/proj2017/final-reports/5244110.pdf)  
35. NeuSE: A Neural Snapshot Ensemble Method for Collaborative Filtering \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/pdf/2104.07269](https://arxiv.org/pdf/2104.07269)  
36. Understanding Test-Time Augmentation \- arXiv, 访问时间为 七月 29, 2025， [https://arxiv.org/html/2402.06892v1](https://arxiv.org/html/2402.06892v1)  
37. Test-Time Augmentation for Document Image Binarization \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/publication/371840068\_Test-Time\_Augmentation\_for\_Document\_Image\_Binarization](https://www.researchgate.net/publication/371840068_Test-Time_Augmentation_for_Document_Image_Binarization)  
38. (PDF) Understanding Test-Time Augmentation \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/publication/356792655\_Understanding\_Test-Time\_Augmentation](https://www.researchgate.net/publication/356792655_Understanding_Test-Time_Augmentation)  
39. Test-Time Augmentation For Tabular Data With Scikit-Learn \- MachineLearningMastery.com, 访问时间为 七月 29, 2025， [https://machinelearningmastery.com/test-time-augmentation-with-scikit-learn/](https://machinelearningmastery.com/test-time-augmentation-with-scikit-learn/)  
40. Feature Augmentation Based Test-Time Adaptation \- CVF Open Access, 访问时间为 七月 29, 2025， [https://openaccess.thecvf.com/content/WACV2025/papers/Cho\_Feature\_Augmentation\_Based\_Test-Time\_Adaptation\_WACV\_2025\_paper.pdf](https://openaccess.thecvf.com/content/WACV2025/papers/Cho_Feature_Augmentation_Based_Test-Time_Adaptation_WACV_2025_paper.pdf)  
41. When and Why Test-Time Augmentation Works \- ResearchGate, 访问时间为 七月 29, 2025， [https://www.researchgate.net/publication/346143761\_When\_and\_Why\_Test-Time\_Augmentation\_Works](https://www.researchgate.net/publication/346143761_When_and_Why_Test-Time_Augmentation_Works)  
42. The Geometric Median on Riemannian Manifolds with Application to ..., 访问时间为 七月 29, 2025， [https://pmc.ncbi.nlm.nih.gov/articles/PMC2735114/](https://pmc.ncbi.nlm.nih.gov/articles/PMC2735114/)  
43. Estimating Outlier-Immunized Common Harmonic Waves for Brain Network Analyses on the Stiefel Manifold \- PubMed, 访问时间为 七月 29, 2025， [https://pubmed.ncbi.nlm.nih.gov/37028067/](https://pubmed.ncbi.nlm.nih.gov/37028067/)  
44. torch.Tensor — PyTorch 2.7 documentation, 访问时间为 七月 29, 2025， [https://pytorch.org/docs/stable/tensors.html](https://pytorch.org/docs/stable/tensors.html)