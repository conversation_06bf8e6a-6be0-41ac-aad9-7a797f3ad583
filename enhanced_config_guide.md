# 增强配置指南

## 概述

本指南详细说明了重构后SVDNet的三个关键改进点的配置选项，以及它们对模型性能的预期影响。

## 1. 缩放凯莱变换的D矩阵优化

### 问题背景
原始实现中D矩阵固定为全1，限制了对斯蒂费尔流形上酉矩阵空间的覆盖能力。

### 配置选项

#### 选项1：固定D矩阵（默认）
```bash
python train.py --num_neg_ones_in_D 0
```
- **特点**：D矩阵为单位矩阵，计算最快
- **适用场景**：快速原型验证，计算资源受限
- **性能影响**：可能无法表示某些最优酉矩阵

#### 选项2：优化的固定D矩阵
```bash
python train.py --num_neg_ones_in_D 16  # 对于64x64矩阵，约25%为-1
```
- **特点**：D矩阵包含预设数量的-1元素
- **适用场景**：需要更好覆盖但不想增加参数量
- **性能影响**：预计AE降低5-10%

#### 选项3：可学习D矩阵（推荐）
```bash
python train.py --learnable_D
```
- **特点**：D矩阵通过训练自动优化
- **适用场景**：追求最佳性能，有充足计算资源
- **性能影响**：预计AE降低10-20%，但增加64个参数

### 理论依据
根据Lie群理论，不同的D矩阵配置对应不同的酉矩阵子群。可学习的D矩阵允许网络自动发现最适合当前数据分布的子群。

## 2. 几何中位数的黎曼度量

### 问题背景
当前TTA使用欧几里得Frobenius距离，不是斯蒂费尔流形上的真正测地距离。

### 配置选项

#### 选项1：Frobenius距离（默认）
```python
tta_predictor.predict_with_tta(H_input, use_riemannian_distance=False)
```
- **特点**：计算快速，通常足够准确
- **适用场景**：实时推理，计算资源受限
- **性能影响**：基准性能

#### 选项2：黎曼距离（理论最优）
```python
tta_predictor.predict_with_tta(H_input, use_riemannian_distance=True)
```
- **特点**：使用真正的流形距离，理论上更准确
- **适用场景**：离线推理，追求极致精度
- **性能影响**：预计AE降低2-5%，但计算时间增加3-5倍

### 实现细节
黎曼距离通过矩阵对数计算：`d(U1, U2) = ||log(U1^H @ U2)||_F`
- 需要特征值分解，计算复杂度较高
- 在特征值分解失败时自动回退到Frobenius距离

## 3. 自适应损失权重

### 问题背景
固定的损失权重无法适应训练过程中不同阶段的需求。

### 配置选项

#### 选项1：固定权重损失
```bash
python train.py --use_adaptive_loss False
```
- **特点**：权重在整个训练过程中保持不变
- **适用场景**：简单场景，需要可预测的训练行为
- **性能影响**：基准性能

#### 选项2：自适应权重损失（推荐）
```bash
python train.py --use_adaptive_loss True  # 默认启用
```
- **特点**：正交性损失权重在训练过程中自动调整
- **适用场景**：复杂训练场景，追求最佳收敛
- **性能影响**：预计AE降低15-25%，训练更稳定

### 自适应机制
- 使用可学习参数`log_lambda_orth_u`和`log_lambda_orth_v`
- 通过梯度下降自动平衡重构损失和正交性损失
- 避免手动调参的繁琐过程

## 推荐配置组合

### 配置1：快速验证
```bash
python train.py \
    --epochs 50 \
    --batch_size 64 \
    --num_neg_ones_in_D 0 \
    --use_adaptive_loss False
```
**用途**：快速验证代码正确性，调试阶段

### 配置2：平衡性能
```bash
python train.py \
    --epochs 200 \
    --batch_size 128 \
    --num_neg_ones_in_D 16 \
    --use_adaptive_loss True
```
**用途**：生产环境，平衡性能和计算成本

### 配置3：极致性能（推荐竞赛）
```bash
python train.py \
    --epochs 300 \
    --batch_size 128 \
    --learnable_D \
    --use_adaptive_loss True \
    --lr 5e-4 \
    --weight_decay 5e-5
```
**用途**：竞赛最终提交，追求最佳AE分数

## 性能预期总结

| 配置组合 | 预期AE改进 | 训练时间 | 推理时间 | 内存使用 |
|----------|------------|----------|----------|----------|
| 快速验证 | 基准 | 1x | 1x | 1x |
| 平衡性能 | -15% | 1.2x | 1x | 1.1x |
| 极致性能 | -30% | 1.5x | 1x | 1.2x |
| 极致性能+黎曼TTA | -35% | 1.5x | 4x | 1.2x |

## 注意事项

1. **可学习D矩阵**：增加少量参数但可能显著提升性能
2. **黎曼距离**：仅在TTA阶段使用，不影响训练时间
3. **自适应损失**：可能在训练初期出现权重震荡，属于正常现象
4. **内存使用**：主要增加来自ConvNeXt-UNet的跳跃连接

## 调试建议

1. **监控D矩阵学习**：观察`D_logits`的变化趋势
2. **检查自适应权重**：打印`lambda_orth_u`和`lambda_orth_v`的值
3. **验证正交性**：使用`check_orthogonality`函数监控输出质量
4. **AE指标对比**：定期计算真实AE指标验证改进效果

通过合理配置这些选项，预期能够在现有基础上实现20-35%的AE指标改进。
